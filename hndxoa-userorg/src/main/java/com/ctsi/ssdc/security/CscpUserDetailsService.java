package com.ctsi.ssdc.security;

import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.enums.RoleEnum;
import com.ctsi.hndx.enums.UserType;
import com.ctsi.hndx.utils.RequestContextUtil;
import com.ctsi.ssdc.admin.domain.dto.CscpMenusDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpRolesDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpMenusService;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserRoleService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import javax.servlet.http.HttpServletRequest;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Authenticate a user from the database.
 */
public class CscpUserDetailsService implements UserDetailsService {

    private final Logger log = LoggerFactory.getLogger(CscpUserDetailsService.class);

    private final CscpUserService cscpUserService;
    private final CscpMenusService cscpMenusService;
    private final CscpOrgService cscpOrgService;
    private final CscpUserRoleService cscpUserRoleService;

    public CscpUserDetailsService(CscpUserService cscpUserService, CscpMenusService cscpMenusService, CscpOrgService cscpOrgService, CscpUserRoleService cscpUserRoleService) {
        this.cscpUserService = cscpUserService;
        this.cscpMenusService = cscpMenusService;
        this.cscpOrgService = cscpOrgService;
        this.cscpUserRoleService = cscpUserRoleService;
    }

    @Override
    public UserDetails loadUserByUsername(final String login) {
        log.debug("Authenticating {}", login);
        HttpServletRequest request = RequestContextUtil.getRequest();
        String requestURI = request.getRequestURI();
        if(requestURI.contains("/login2")){
            //判断是否是使用优化后的登录
            return getCscpUserDetail2(login);
        }else {
            return getCscpUserDetail(login);
        }

    }

    private CscpUserDetail getCscpUserDetail(String login) {
        return cscpUserService.findByCurrentUserName(login).map(user -> createSpringSecurityUser(login, user))
                .orElseThrow(() -> new UsernameNotFoundException("User  " + login + " was not found"));
    }


    public UserDetails getCscpUserDetail2(final String login) {
        log.debug("Authenticating {}", login);

        HttpServletRequest request = RequestContextUtil.getRequest();
        String requestURI = request.getRequestURI();

        return cscpUserService.findByCurrentUserName2(login)
                .map(user -> buildUserDetailWithPerformanceOptimization(user, requestURI))
                .orElseThrow(() -> new UsernameNotFoundException("User " + login + " was not found"));
    }

    private CscpUserDetail buildUserDetailWithPerformanceOptimization(CscpUserDTO user, String requestURI) {

        // 获取当前SecurityContext
        SecurityContext securityContext = SecurityContextHolder.getContext();
        // 并发加载组织和角色信息
        CompletableFuture<CscpOrgDTO> orgFuture = CompletableFuture.supplyAsync(() ->{
            SecurityContextHolder.setContext(securityContext);
           return cscpOrgService.findOne(user.getCompanyId());
        });
        CompletableFuture<List<CscpRolesDTO>> rolesFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            return   cscpUserRoleService.selectRolesByUserId(user.getId());
        });

        // 权限信息加载
        Set<String> grantedAuthorities = new HashSet<>();
        if (shouldLoadAuthorities(requestURI)) {
            List<CscpMenusDTO> menus = cscpMenusService.findByUserIdNoUserCache2(user.getId());
            if (menus != null) {
                menus.forEach(menu -> {
                    if (StringUtils.isNotEmpty(menu.getPermissionCode())) {
                        grantedAuthorities.add(menu.getPermissionCode());
                    }
                });
            }
        }

        // 等待异步结果
        CscpOrgDTO org = orgFuture.join();
        List<CscpRolesDTO> roles = rolesFuture.join();

        // 构建用户详情
        CscpUserDetail detail = new CscpUserDetail(
                user.getId(),
                user.getLoginName(),
                user.getPassword(),
                Collections.unmodifiableSet(grantedAuthorities)
        );
        detail.setMobile(user.getMobile());
        detail.setRealName(user.getRealName());
        detail.setCompanyId(user.getCompanyId());
        detail.setDepartmentId(user.getDepartmentId());
        detail.setTenantId(user.getTenantId());
        detail.setStrId(user.getStrId());
        detail.setDepartmentName(user.getDepartmentName());
        detail.setCompanyName(user.getCompanyName());
        detail.setOrderBy(user.getOrderBy());

        detail.setHasWatermark(user.getHasWatermark());

        detail.setSplitview(user.getSplitview());

        detail.setCloudDiskSpaceSize(user.getCloudDiskSpaceSize());
        if (user.getUserType() != null) {
            detail.setUserType(user.getUserType().name().toLowerCase());
        }
        detail.setStartNo(user.getStartNo());
        detail.setStampUrl(user.getStampUrl());

        // 设置其他属性...
        setOrganizationDetails(detail, org);
        if (CollectionUtils.isNotEmpty(roles)) {
            detail.setRoleCodes(roles.stream()
                    .map(CscpRolesDTO::getRoleCode)
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList()));
            if (detail.getRoleCodes().contains(RoleEnum.SYSTEM_ROLE.getCode())) {
                detail.setUserType(UserType.SYSTEM_USER.name().toLowerCase());
                detail.setTenantId(null);
                detail.setCompanyId(null);
            }
        }
        detail.setModeratorAppIdList(cscpUserRoleService.getModeratorManageAppIds(user.getId()));

        return detail;
    }
    private boolean shouldLoadAuthorities(String requestURI) {
        return !requestURI.contains("/api/openapi/system/v1/createToken") && !requestURI.contains("/api/system/loginNew");
    }

    private void setOrganizationDetails(CscpUserDetail detail, CscpOrgDTO org) {
        if (org == null) {
            return;
        }
        detail.setSmsName(org.getSmsName());
        detail.setSmsKey(org.getSmsKey());
        detail.setOid(org.getOid());
        detail.setCreditCode(org.getCreditCode());
        detail.setDzCode(org.getDzCode());
        detail.setOrganizationCode(org.getOrganizationCode());
        detail.setCode(org.getCode());
//        detail.setPathCode(org.getPathCode());
        detail.setDossierNumber(org.getDossierNumber());
        detail.setOrgIdPath(org.getOrgIdPath());
        detail.setOrgCodePath(org.getOrgCodePath());
    }

    @Async(value = SysConstant.EXECUTOR_ASYNC_NAME)
    protected CscpUserDetail createSpringSecurityUser(String lowercaseLogin, CscpUserDTO user) {
        // if (!user.getActivated()) {
        // throw new UserNotActivatedException("User " + lowercaseLogin + " was
        // not
        // activated");
        // }
        CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(user.getCompanyId());
        Set<String> grantedAuthorities = new HashSet<>();


        // 获取当前请求
        HttpServletRequest request = RequestContextUtil.getRequest();
        // 获取请求的URI，判断controller
        String requestURI = request.getRequestURI();
        if(requestURI.contains("/api/openapi/system/v1/createToken") || requestURI.contains("/api/system/loginNew")){
            //单点登录的接口不需要记录菜单权限


        }else {
            List<CscpMenusDTO> cscpMenusDTOList = cscpMenusService.findByUserIdNoUserCache(user.getId());
            cscpMenusDTOList.forEach(menu -> {
                if (StringUtils.isNotEmpty(menu.getPermissionCode())) {
                    grantedAuthorities.add(menu.getPermissionCode());
                }
            });
        }

        CscpUserDetail cscpUserDetail = new CscpUserDetail(user.getId(), user.getLoginName(), user.getPassword(), grantedAuthorities);
        cscpUserDetail.setMobile(user.getMobile());
        cscpUserDetail.setRealName(user.getRealName());
        cscpUserDetail.setCompanyId(user.getCompanyId());
        cscpUserDetail.setDepartmentId(user.getDepartmentId());
        cscpUserDetail.setTenantId(user.getTenantId());
        cscpUserDetail.setStrId(user.getStrId());
        cscpUserDetail.setDepartmentName(user.getDepartmentName());
        cscpUserDetail.setCompanyName(user.getCompanyName());
        cscpUserDetail.setOrderBy(user.getOrderBy());
        // TODO 设置是否水印 2023-03-24 added lizuolang
        cscpUserDetail.setHasWatermark(user.getHasWatermark());
        // TODO 设置正文编辑是否分屏 2023-04-19 added lizuolang
        cscpUserDetail.setSplitview(user.getSplitview());
        // TODO 设置单位共享云盘空间大小，单位GB，默认2G 2023-05-05 added lizuolang
        cscpUserDetail.setCloudDiskSpaceSize(user.getCloudDiskSpaceSize());
        if (user.getUserType() != null) {
            cscpUserDetail.setUserType(user.getUserType().name().toLowerCase());
        }
        cscpUserDetail.setSmsName(cscpOrgDTO.getSmsName());
        cscpUserDetail.setSmsKey(cscpOrgDTO.getSmsKey());

        cscpUserDetail.setOid(cscpOrgDTO.getOid());
        cscpUserDetail.setCreditCode(cscpOrgDTO.getCreditCode());
        cscpUserDetail.setDzCode(cscpOrgDTO.getDzCode());
        cscpUserDetail.setOrganizationCode(cscpOrgDTO.getOrganizationCode());

        cscpUserDetail.setCode(cscpOrgDTO.getCode());
//        cscpUserDetail.setPathCode(cscpOrgDTO.getPathCode());

        cscpUserDetail.setDossierNumber(cscpOrgDTO.getDossierNumber());

        // add orgIdPath and orgCodePath
        cscpUserDetail.setOrgIdPath(cscpOrgDTO.getOrgIdPath());
        cscpUserDetail.setOrgCodePath(cscpOrgDTO.getOrgCodePath());

        cscpUserDetail.setStartNo(user.getStartNo());
        cscpUserDetail.setStampUrl(user.getStampUrl());

        List<CscpRolesDTO> roles = cscpUserRoleService.selectRolesByUserId(user.getId());
        if (CollectionUtils.isNotEmpty(roles)) {
            cscpUserDetail.setRoleCodes(roles.stream().map(CscpRolesDTO::getRoleCode).filter(StringUtils::isNotEmpty).collect(Collectors.toList()));
            if (cscpUserDetail.getRoleCodes().contains(RoleEnum.SYSTEM_ROLE.getCode())) {
                cscpUserDetail.setUserType(UserType.SYSTEM_USER.name().toLowerCase());
                cscpUserDetail.setTenantId(null);
                cscpUserDetail.setCompanyId(null);
            }
        }
        cscpUserDetail.setModeratorAppIdList(cscpUserRoleService.getModeratorManageAppIds(user.getId()));
        return cscpUserDetail;
    }


}

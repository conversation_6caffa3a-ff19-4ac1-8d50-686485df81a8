package com.ctsi.ssdc.admin.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.io.file.FileWriter;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.addrbook.entity.TLabelOrg;
import com.ctsi.hndx.addrbook.entity.dto.TLabelOrgDTO;
import com.ctsi.hndx.addrbook.mapper.TLabelOrgMapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.encryption.KeyCenterUtils;
import com.ctsi.hndx.enums.OrgTypeEnum;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.systenant.entity.TSysTenant;
import com.ctsi.hndx.systenant.mapper.TSysTenantMapper;
import com.ctsi.hndx.systenant.service.ITSysTenantService;
import com.ctsi.hndx.tree.Node;
import com.ctsi.hndx.tree.impl.BaseTreeCurdServiceImpl;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.domain.*;
import com.ctsi.ssdc.admin.domain.dto.*;
import com.ctsi.ssdc.admin.domain.vo.MainOrgVO;
import com.ctsi.ssdc.admin.repository.*;
import com.ctsi.ssdc.admin.service.*;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.service.ITDeptManagementAuthorityService;
import com.ctsi.ssdc.util.RedisUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.apache.commons.lang.StringUtils.startsWith;

/**
 * Service Implementation for managing CscpOrg.
 *
 * <AUTHOR> biyi generator
 */
@Service
public class CscpOrgServiceImpl extends BaseTreeCurdServiceImpl<CscpOrgDTO, CscpOrg, CscpOrgRepository>
        implements CscpOrgService {

    protected static final Integer ORG_CODE_LEN = 4;

    private final Logger log = LoggerFactory.getLogger(CscpOrgServiceImpl.class);

    //用于区分查询的是否是本租户下面的单位（不为空说明是外单位，为空说明是本单位）
    private static ThreadLocal<Long> longThreadLocal = new ThreadLocal<>();


    // 如果是单位登录的时候，true表示不去过滤虚拟机构
    private static ThreadLocal<Boolean> ORGTYPE1ThreadLocal = new ThreadLocal<>();


    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private CscpWorkGroupOrgRepository cscpWorkGroupOrgRepository;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private OrgSaveContext context;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private ITSysTenantService sysTenantService;

    @Autowired
    private TLabelOrgMapper tLabelOrgMapperl;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private TSysTenantMapper tSysTenantMapper;

    @Resource
    private CscpRolesRepository cscpRolesRepository;

    @Resource
    private CscpUserRoleService cscpUserRoleService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CompanySaveStrategyServiceImpl companySaveStrategyServiceImpl;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private CscpOrgAuditRemoveRepository cscpOrgAuditRemoveRepository;

    @Autowired
    private CscpOrgChangeHistoryService cscpOrgChangeHistoryService;
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private ITDeptManagementAuthorityService tDeptManagementAuthorityService;


    private static final Long ROOT_ORG_ID = 0L; //最顶层机构默认父级ID

    private static final String ROOT_ORG_CODE = "0001"; //最顶层机构编码

    private static final String ROOT_ORG_PATH_CODE = "0001"; //最顶层机构完整编码

    private static final String ORG_CODE_SUFFIX = "01"; //新建下级部门，部门编码新增两位"01"

    private static final Integer ROOT_ORG_LEVEL = 1; //最顶层机构级别

    /**
     * insert a cscpOrg.
     *
     * @param cscpOrgDTO the entity to insert
     * @return the persisted entity
     */
    @Override
    public CscpOrgDTO insert(CscpOrgDTO cscpOrgDTO) {
        log.debug("Request to insert CscpOrg : {}", cscpOrgDTO);

        CscpOrg cscpOrg = new CscpOrg();
        BeanUtils.copyProperties(cscpOrgDTO, cscpOrg);

        if (westoneEncryptService.isCipherMachine()) {
            if (null != cscpOrg.getOrgCode() && !"".equals(cscpOrg.getOrgCode())) {
                cscpOrg.setHmacOrgCode(westoneEncryptService.calculateSM3HMAC(cscpOrg.getOrgName() + cscpOrg.getOrgCode()));
            }
        }

        cscpOrgRepository.insert(cscpOrg);

        BeanUtils.copyProperties(cscpOrg, cscpOrgDTO);
        return cscpOrgDTO;
    }

    /**
     * update a cscpOrg.
     *
     * @param cscpOrgDTO the entity to update
     * @return the persisted entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    // todo 缓存机构 @CachePut(value = "orgCache-treeNodes", key = "#cscpOrgDTO.id", unless = "#result == null || #result.isEmpty()")
    public List<Long> update(CscpOrgDTO cscpOrgDTO) {
        log.info("CscpOrgServiceImpl.update cscpOrgDTO = {}", cscpOrgDTO);
//        // 把大于该排序号的所有排序号都 + 1
//        context.executeUpdateSortStrategy(cscpOrgDTO);
        cscpOrgDTO.validate(cscpOrgService);
        // 插入组织机构
        CscpOrg cscpOrg = new CscpOrg();
        //获取机构层级
        int leave = this.getOrgLevel(cscpOrgDTO.getParentId());
        cscpOrg.setLevel(leave);
        //判断排序号在当前父节点下是否存在，存在把大于等于该排序号
        this.updateOrgSort(cscpOrgDTO);
        // 弃用 pathCode start
        // 获取机构编码
        // String pathCode = this.getOrgCodeAndPathCode(cscpOrgDTO);
        // cscpOrgDTO.setPathCode(pathCode);
        // 重置机构编码
        // this.setPathCode(cscpOrgDTO);
        // 弃用 pathCode end

        BeanUtils.copyProperties(cscpOrgDTO, cscpOrg);
        if(cscpOrgDTO.getModelDataTypeList()!=null && cscpOrgDTO.getModelDataTypeList().size()>0){
            String modelDataType = cscpOrgDTO.getModelDataTypeList().stream().collect(Collectors.joining(","));
            cscpOrg.setModelDataType(modelDataType);
        }

        if(cscpOrgDTO.getModelNameList()!=null && cscpOrgDTO.getModelNameList().size()>0){
            String modelName = cscpOrgDTO.getModelNameList().stream().collect(Collectors.joining(","));
            cscpOrg.setModelName(modelName);
        }
        LambdaUpdateWrapper<CscpOrg> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(cscpOrg.getManagementDefaultCode()==null, CscpOrg::getManagementDefaultCode, null);
        updateWrapper.set(cscpOrg.getOrgLevelType()==null, CscpOrg::getOrgLevelType, null);
        updateWrapper.set(cscpOrg.getOrgClassify()==null, CscpOrg::getOrgClassify, null);
        updateWrapper.set(cscpOrg.getOrgLabel()==null, CscpOrg::getOrgLabel, null);
        updateWrapper.eq(CscpOrg::getId,cscpOrg.getId());

        //获取修改机构数据，如果父级发生改变，则修改机构编码和行政区域编码
        CscpOrg orgModel = cscpOrgRepository.selectById(cscpOrgDTO.getId());
        List<Long> result = new ArrayList<>();
        List<Long> result2 = new ArrayList<>();
        List<CscpOrg> result3 = new ArrayList<>();
        Map<Long, CscpOrg> resultMap = new HashMap<>();
        result.add(cscpOrgDTO.getId());
        result2.add(cscpOrgDTO.getId());
        if(orgModel.getParentId().longValue()!=cscpOrgDTO.getParentId().longValue()) {
            //重新重新编号，并修改行政区域编码
            CscpOrg org = getMaxCodeCscpOrg(cscpOrgDTO);
            // List<CscpOrg> nextOrgs = cscpOrgRepository.getAllOrgCodeByParentId(cscpOrgDTO.getParentId());
            //获取上级机构信息
            CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
            // 父级不能设置为之前自己的子级
            if (startsWith(parentOrg.getOrgCodePath(), orgModel.getOrgCodePath())) {
                throw new BusinessException("父级机构不能设置为之前的子级机构");
            }
            String orgCode = "";
            if (org == null) {
                orgCode = parentOrg.getOrgCode() + "0001";
                cscpOrg.setMaxNumber(1);
            } else {
                BigInteger bigIntCode = new BigInteger(org.getOrgCode());
                BigInteger newCode = bigIntCode.add(BigInteger.ONE);
                int maxNumber = org.getMaxNumber() + 1;
                orgCode = newCode.toString();
                cscpOrg.setMaxNumber(maxNumber);
            }
            cscpOrg.setOrgCode(orgCode);

            if (westoneEncryptService.isCipherMachine()) {
                if (null != cscpOrg.getOrgCode() && !"".equals(cscpOrg.getOrgCode())) {
                    cscpOrg.setHmacOrgCode(westoneEncryptService.calculateSM3HMAC(cscpOrg.getOrgName() + cscpOrg.getOrgCode()));
                }
            }

            cscpOrg.setRegionCode(parentOrg.getRegionCode());

            cscpOrg.setOrgIdPath(StringUtils.isNotEmpty(parentOrg.getOrgIdPath()) ?
                    (parentOrg.getOrgIdPath() + "|" + parentOrg.getId()) : parentOrg.getId().toString());
            cscpOrg.setOrgCodePath(StringUtils.isNotEmpty(parentOrg.getOrgCodePath()) ?
                    (parentOrg.getOrgCodePath() + "|" + orgCode) : orgCode);
            cscpOrg.setOrderByPath(StringUtils.isNotEmpty(parentOrg.getOrderByPath()) ?
                    (parentOrg.getOrderByPath() + (100000 + cscpOrg.getOrderBy())) : String.valueOf(100000 + cscpOrg.getOrderBy()));
            // 单位处理
            if (cscpOrg.getType() != 2) {
                if (parentOrg.getType() != 2 && parentOrg.getId().equals(parentOrg.getCompanyId())) {
                    cscpOrg.setCompanyId(cscpOrg.getId());
                } else {
                    cscpOrg.setCompanyId(parentOrg.getCompanyId());
                }
            } else {
                cscpOrg.setCompanyId(cscpOrg.getId());
            }

            //修改下级机构的所属区域编码
            LambdaQueryWrapper<CscpOrg> childOrgWrapper = new LambdaQueryWrapper<>();
            if (StringUtils.isNotEmpty(orgModel.getOrgCodePath())) {
                childOrgWrapper.like(CscpOrg::getOrgCodePath, orgModel.getOrgCodePath());
            } else {
                childOrgWrapper.like(CscpOrg::getOrgCode,orgModel.getOrgCode());
            }
            childOrgWrapper.ne(CscpOrg::getOrgCode,orgModel.getOrgCode());
            List<CscpOrg> listOrg = cscpOrgRepository.selectListNoAdd(childOrgWrapper);
            String finalOrgCode = orgCode;
            String pushFlag = sysConfigService.getSysConfigValueByCode(SysConfigConstant.CSCP_ORG_DESCENDANTS_PUSH);
            listOrg.sort(Comparator.comparing(cscpOrg1 -> cscpOrg1.getOrgIdPath().length()));
            Map<Long, CscpOrg> cscpOrgMap = new HashMap<>();
            listOrg.forEach(item->{
                int index = orgModel.getOrgCode().length();
                String childOrgCode = finalOrgCode + item.getOrgCode().substring(index);
                item.setOrgCode(childOrgCode);

                if (westoneEncryptService.isCipherMachine()) {
                    if (null != item.getOrgCode() && !"".equals(item.getOrgCode())) {
                        // TODO 计算SM3HMAC
                        item.setHmacOrgCode(westoneEncryptService.calculateSM3HMAC(item.getOrgName() + item.getOrgCode()));
                    }
                }

                item.setRegionCode(cscpOrg.getRegionCode());
                // 修改orgIdPath 、 orgCodePath
                if (!StringUtils.isEmpty(item.getOrgIdPath())) {
                    item.setOrgIdPath(item.getOrgIdPath().replace(orgModel.getOrgIdPath(), cscpOrg.getOrgIdPath()));
                    if (item.getOrgIdPath().startsWith("|")) {
                        item.setOrgIdPath(StringUtils.substring(item.getOrgIdPath(), 1));
                    }
                }
                if (!StringUtils.isEmpty(item.getOrgCodePath())) {
                    item.setOrgCodePath(item.getOrgCodePath().replace(orgModel.getOrgCodePath(), cscpOrg.getOrgCodePath()));
                    if (item.getOrgCodePath().startsWith("|")) {
                        item.setOrgCodePath(StringUtils.substring(item.getOrgCodePath(), 1));
                    }
                }
                if (!StringUtils.isEmpty(item.getOrderByPath())
                        && StringUtils.isNotEmpty(orgModel.getOrderByPath()) && StringUtils.isNotEmpty(cscpOrg.getOrderByPath())) {
                    item.setOrderByPath(item.getOrderByPath().replace(orgModel.getOrderByPath(), cscpOrg.getOrderByPath()));
                }
                // 单位Id处理
                if (item.getType() == 2) {
                    item.setCompanyId(item.getId());
                    cscpOrgMap.put(item.getId(), item);
                } else {
                    item.setCompanyId(cscpOrg.getCompanyId());
                    CscpOrg itemParent = cscpOrgMap.get(item.getParentId());
                    if (itemParent != null) {
                        item.setCompanyId(itemParent.getCompanyId());
                    }
                }
                cscpOrgRepository.updateById(item);
                if ("1".equals(pushFlag)) {
                    result.add(item.getId());
                }
                result2.add(item.getId());

                resultMap.put(item.getId(), item);
            });
            // 记录机构变更记录
            CscpOrgChangeHistory cscpOrgChangeHistory = new CscpOrgChangeHistory();
            cscpOrgChangeHistory.initByOldNewOrg(orgModel, cscpOrg);
            cscpOrgChangeHistory.setPosterity(result2.stream().map(String::valueOf).collect(Collectors.joining(",")));
            cscpOrgChangeHistory.setPushKzFlag(StringUtils.isNotEmpty(pushFlag) ? Integer.parseInt(pushFlag) : 0);
            cscpOrgChangeHistory.insertOne(cscpOrgChangeHistoryService);
            // 机构下的用户单位调整并推送
            result3.add(cscpOrg);
            if (CollectionUtils.isNotEmpty(listOrg)) {
                result3.addAll(listOrg);
            }

            // TODO 2025-05-30 机构变更所有涉及的数据应该同步改变; 测试过不能放在else-if外,上述代码内部改变过参数; 需要排查
        } else if (!Objects.equals(cscpOrg.getType(), orgModel.getType())) {
            // 上一个if判断的父级id发生变化才会进去，导致机构在本级发生类型变化，无法触发，所以这里增加判断
            Long companyId = cscpOrg.getId();
            // 入参的机构类型的type不等于机构原有的类型
            if (cscpOrg.getParentId() == 0 || cscpOrg.getType() == 2) {
                // 当机构的类型发生变化,并且变更为2时，机构的company_id需要变更为自身
                cscpOrg.setCompanyId(companyId);
            } else {
                // 获取父级机构,开始向上查找
                CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrg.getParentId());
                int i = 0;
                while (parentOrg.getParentId() != 0L) {
                    if (parentOrg.getType() == 2) {
                        cscpOrg.setCompanyId(parentOrg.getId());
                        break;
                    } else {
                        parentOrg = cscpOrgRepository.selectById(parentOrg.getParentId());
                        i ++;
                        if (i == 5) {
                            // 向上5层找不到单位，直接取自身
                            cscpOrg.setCompanyId(companyId);
                            break;
                        }
                    }
                }
            }
        }

        // 当机构单位的值发生变化时
        if (!Objects.equals(cscpOrg.getCompanyId(), orgModel.getCompanyId())
                || (!Objects.equals(cscpOrg.getType(), orgModel.getType()) && cscpOrg.getType() == 2)) {
            // 获取当前机构下子孙机构
            List<CscpOrg> orgList = cscpOrgRepository.selectOrgChildrenByParentId(cscpOrg.getId());
            if (CollectionUtils.isNotEmpty(orgList)) {
                // TODO 这里org机构下所有的子集中，不等于2的机构、并且所属单位不在子集范围内的都需要做变更
                // 筛选出所有机构中等于单位的数据
                Set<Long> companyIdList = orgList.stream()
                        .filter(c -> Objects.equals(2, c.getType())).map(CscpOrg::getId).collect(Collectors.toSet());
                // 取出子集中companyId 不等于自身和子集中任意机构的数据
                List<CscpOrg> cscpOrgList = orgList.stream().filter(o -> !companyIdList.contains(o.getCompanyId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(cscpOrgList)) {
                    List<Long> ordIdList = cscpOrgList.stream().map(CscpOrg::getId).collect(Collectors.toList());
                    // 机构下所有非等于单位的数据中的companyId都需要变更
                    LambdaUpdateWrapper<CscpOrg> updateOrg = Wrappers.lambdaUpdate();
                    updateOrg.in(CscpOrg::getId, ordIdList);
                    updateOrg.set(CscpOrg::getCompanyId, cscpOrg.getCompanyId());
                    cscpOrgService.update(null, updateOrg);

                    // 去重
                    ordIdList = ordIdList.stream()
                            .distinct()
                            .collect(Collectors.toList());
                    result.addAll(ordIdList);

                    cscpOrgList.forEach(c -> c.setCompanyId(cscpOrg.getCompanyId()));
                    // 去重
                    Map<Long, CscpOrg> map = cscpOrgList.stream().collect(Collectors.toMap(CscpOrg::getId,Function.identity()));
                    cscpOrgList = new ArrayList<>(map.values());
                    result3.addAll(cscpOrgList);
                    if (CollectionUtils.isNotEmpty(cscpOrgList)) {
                        for (CscpOrg childNode : cscpOrgList) {
                            resultMap.putIfAbsent(childNode.getId(), childNode);
                        }
                    }
                }
            }
        }

        int t = cscpOrgRepository.update(cscpOrg,updateWrapper);
        if(cscpOrg.getUnitType()==null){
            cscpOrgRepository.updateUnitTypeById(cscpOrg.getId());
        }

        //单位名称同步
        cscpUserOrgRepository.updateBycompanyId(cscpOrg.getId(),cscpOrg.getOrgName(),cscpOrg.getOrgAbbreviation());
        //部门名称和简称同步
        cscpUserOrgRepository.updateByOrgId(cscpOrg.getId(),cscpOrg.getOrgName(),cscpOrg.getOrgAbbreviation());

        // 编辑分管领导和部门领导
        if (CollectionUtils.isNotEmpty(cscpOrgDTO.getDepartmentHeadIdList())
                || !Objects.isNull(cscpOrgDTO.getBranchLeaderId())) {
            cscpUserService.saveBranchLeaderAndDepartmentHead(cscpOrgDTO);
        }

        try {
            // 设置缓存的 key，使用 Node 的 ID
            String cacheKey = "treeNode:" + cscpOrgDTO.getId();
//            String cacheKeyAll = "treeNode:All" + 0;
            redisUtil.del(cacheKey);
            this.clearCache(0L);
//            redisUtil.del(cacheKeyAll);
        }catch (Exception e){
            log.error("更新treeNode缓存失败");
        }

        if (CollectionUtils.isNotEmpty(result3)) {
            OrgPushUserEvent orgPushUserEvent = new OrgPushUserEvent();
            orgPushUserEvent.setCscpOrgList(result3);
            orgPushUserEvent.setCscpOrgMap(resultMap);
            eventPublisher.publishEvent(orgPushUserEvent);
        }
        return result;
    }

    @Override
    public CscpOrg getMaxCodeCscpOrg(CscpOrgDTO cscpOrgDTO) {
        int retryCount = 0;
        final int maxRetries = 10;

        String redisKey = "getMaxCodeCscpOrg:" + cscpOrgDTO.getParentId();

        while (retryCount <= maxRetries) {
            Boolean locked = redisTemplate.opsForValue().setIfAbsent(redisKey, "1", 5, TimeUnit.SECONDS);
            if (Boolean.TRUE.equals(locked)) {
                try {
                    return cscpOrgRepository.getOneByParentIdMaxNumberDesc(cscpOrgDTO.getParentId());
                } catch (Exception e) {
                    List<CscpOrg> list = cscpOrgRepository.getAllOrgCodeByParentId(cscpOrgDTO.getParentId());

                    Optional<CscpOrg> maxOrgOpt = list.stream()
                            .filter(co -> co.getOrgCode() != null && co.getOrgCode().matches("\\d+"))
                            .max(Comparator.comparing(co -> new BigInteger(co.getOrgCode())));

                    return maxOrgOpt.orElse(null);
                } finally {
                    redisTemplate.delete(redisKey);
                }
            }
            try {
                Thread.sleep(500);
            } catch (InterruptedException ignored) {
                Thread.currentThread().interrupt();
                break;
            }
            retryCount++;
        }
        return null;
    }

    @Override
    public MainOrgVO queryUnitOrgTreeByName(String orgName) {
        MainOrgVO orgVO = new MainOrgVO();
        if (StringUtils.isNotEmpty(orgName)) {
            // 构建查询条件
            LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.like(CscpOrg::getOrgName, orgName);
            // 查询匹配机构
            List<CscpOrg> matchedOrgs = cscpOrgRepository.selectListNoAdd(lambdaQueryWrapper);
            if (CollectionUtils.isEmpty(matchedOrgs)) {
                return orgVO;
            }

            Set<Long> allIds = collectAllOrgIds(matchedOrgs);
            if (!allIds.isEmpty()) {
                lambdaQueryWrapper.clear();
                lambdaQueryWrapper.in(CscpOrg::getId, allIds);
                matchedOrgs = cscpOrgRepository.selectListNoAdd(lambdaQueryWrapper);
            }
            List<CscpMainOrgVO> copy = ListCopyUtil.copy(matchedOrgs, CscpMainOrgVO.class);

            List<Node<CscpMainOrgVO>> tree = mainBuildTree(copy);
            orgVO.setOrgTree(tree);
        }
        return orgVO;
    }

    @Override
    public PageResult<CscpOrgDTO> selectDeletedList(String orgName,String orgCode,BasePageForm basePageForm) {
        IPage deletedList = cscpOrgRepository.selectDeletedList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), orgName,
                orgCode);

        IPage<CscpOrgDTO> data = deletedList.convert(entity -> BeanConvertUtils.copyProperties(entity, CscpOrgDTO.class));
        return  new PageResult(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    @Override
    public boolean deleteRealById(Long id) {


        return cscpOrgRepository.deleteRealById(id);
    }


    private CscpOrg getMaxOrgCode(List<CscpOrg> orgs) {
        Optional<CscpOrg> maxOrg = orgs.stream()
                .filter(org -> org.getOrgCode().matches("\\d+"))
                .max(Comparator.comparing(org -> parseOrgCode(org.getOrgCode())));
        return maxOrg.orElse(orgs.get(0));
    }

    private BigDecimal parseOrgCode(String orgCode) {
        try {
            return new BigDecimal(orgCode);
        } catch (NumberFormatException e) {
            // 可记录日志
            return BigDecimal.ZERO; // 或者返回负无穷表示无效
        }
    }

    /**
     * Get all the cscpOrgs.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public PageResult<CscpOrgDTO> findAll() {
        log.debug("Request to get all CscpOrgs");

//        List<CscpOrgDTO> data = cscpOrgRepository.selectList(null).stream().map(cscpOrgMapper::toDto)
//                .collect(Collectors.toCollection(LinkedList::new));
        List<CscpOrgDTO> data = ListCopyUtil.copy(cscpOrgRepository.selectList(null), CscpOrgDTO.class);

        long count = 0L;

        if (CollectionUtils.isNotEmpty(data)) {
            count = cscpOrgRepository.selectCount(null);
        }

        return new PageResult<CscpOrgDTO>(data, count, count);

    }

    /**
     * Get one cscpOrg.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public CscpOrgDTO findOne(Long id) {
        log.debug("Request to get CscpOrg : {} ", id);
        CscpOrgDTO dto = new CscpOrgDTO();

        CscpOrg cscpOrg = cscpOrgRepository.selectById(id);
        if (cscpOrg != null) {
            BeanUtils.copyProperties(cscpOrg, dto);
        }

        return dto;
    }

    private String getPageOrderBy(Pageable page) {
        log.info("CscpOrgServiceImpl.getPageOrderBy Pageable = {}", page);

        if (page != null && page.getSort() != null) {

            StringBuilder sb = new StringBuilder();

            page.getSort()
                    .forEach(sort -> sb.append(sort.getProperty()).append(" ").append(sort.getDirection()).append(","));

            if (sb.length() > 1) {
                return (sb.substring(0, sb.length() - 1));
            }
        }

        return null;
    }

    /**
     * Get the cscpOrgs.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public PageResult<CscpOrgDTO> findByCscpOrgDTO(CscpOrgDTO cscpOrgDTO, BasePageForm basePageForm) {
        log.info("CscpOrgServiceImpl.findByCscpOrgDTO CscpOrgDTO = {}", cscpOrgDTO);

        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();

        if (cscpOrgDTO.getId() != null) {
            queryWrapper.eq(CscpOrg::getId, cscpOrgDTO.getId());
        }
        if (cscpOrgDTO.getParentId() != null) {
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
        }

        if (StringUtils.isNotEmpty(cscpOrgDTO.getOrgName())) {
            queryWrapper.like(CscpOrg::getOrgName, cscpOrgDTO.getOrgName());
        }
        if (StringUtils.isNotEmpty(cscpOrgDTO.getDescription())) {
            queryWrapper.like(CscpOrg::getDescription, cscpOrgDTO.getDescription());
        }
        IPage<CscpOrg> cscpLogLoginIPage =
                cscpOrgRepository.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        List<CscpOrgDTO> data = ListCopyUtil.copy(cscpLogLoginIPage.getRecords(), CscpOrgDTO.class);

        long count = 0L;

        if (CollectionUtils.isNotEmpty(data)) {
            count = cscpLogLoginIPage.getTotal();
        }

        return new PageResult<CscpOrgDTO>(data, count, count);

    }


    @Override
    @Transactional
    public CscpOrgDTO save(CscpOrgDTO cscpOrgDTO) {
        log.info("CscpOrgServiceImpl.save cscpOrgDTO = {}", cscpOrgDTO);
        if (Objects.isNull(cscpOrgDTO.getParentId())) {
            throw new BusinessException("上级节点不能为空");
        }
        // 是否考核设置默认值
        if (Objects.isNull(cscpOrgDTO.getCheckUp())) {
            cscpOrgDTO.setCheckUp(false);
        }
        cscpOrgDTO.validate(cscpOrgService);
        CscpOrgDTO dto = context.executeStrategy(cscpOrgDTO);
        BeanUtils.copyProperties(dto, cscpOrgDTO);
        this.clearCache(cscpOrgDTO.getParentId());
        return cscpOrgDTO;
    }

    public String getOrgCodeAndPathCode(CscpOrgDTO cscpOrgDTO) {
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
        queryWrapper.lt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);

        // 如果插入的单位为顶级
        if (ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
            if (CollectionUtils.isEmpty(orgList)) {
                String curOrgPathCode = ROOT_ORG_PATH_CODE;
                return curOrgPathCode;
            }
            String curOrgCode = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgList.size() + 1),
                    ORG_CODE_LEN);
            String curOrgPathCode = curOrgCode;
            return curOrgPathCode;
        }

        // 如果插入的单位不是顶级
        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
        if (org.apache.commons.lang3.StringUtils.isBlank(parentOrg.getCode()) && org.apache.commons.lang3.StringUtils.isBlank(parentOrg.getPathCode())) {
            throw new BusinessException("单位上级节点的单位编码和单位完整编码不能为空");
        }
        String parentOrgCode = parentOrg.getCode();
        String parentOrgPathCode = parentOrg.getPathCode();

        String curOrgCode = parentOrgPathCode;
        String curOrgPathCode = parentOrgPathCode + com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgList.size() + 1),
                ORG_CODE_LEN);
        return curOrgPathCode;
    }

    private void setPathCode(CscpOrgDTO cscpOrgDTO) {
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(CscpOrg::getPathCode, cscpOrgDTO.getPathCode());
        lambdaQueryWrapper.ne(CscpOrg::getId, cscpOrgDTO.getId());
        int count = cscpOrgRepository.selectCountNoAdd(lambdaQueryWrapper);

        // 如果存在，则把大于等于该排序的所有单位编码+1
        if (count > 0) {
            CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
            //获取小于当前机构排序号的数量
            LambdaQueryWrapper<CscpOrg> queryCountWrapper = Wrappers.lambdaQuery();
            queryCountWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryCountWrapper.lt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            final Integer[] orgCount = {cscpOrgRepository.selectCountNoAdd(queryCountWrapper)};
            orgCount[0] = orgCount[0] + 1;

            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.ne(CscpOrg::getId, cscpOrgDTO.getId());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            List<CscpOrg> cscpOrg = cscpOrgRepository.selectListNoAdd(queryWrapper);
            List<cscpOrgUpdateCodeDTO> list = new ArrayList<>();
            List<cscpOrgUpdateCodeDTO> finalList = list;
            cscpOrg.forEach(item -> {
                cscpOrgUpdateCodeDTO dto = new cscpOrgUpdateCodeDTO();
                String curOrgPathCode = "";
                if (parentOrg == null) {
                    curOrgPathCode = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgCount[0] + 1),
                            ORG_CODE_LEN);
                } else {
                    curOrgPathCode = parentOrg.getPathCode() + com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgCount[0] + 1),
                            ORG_CODE_LEN);
                }
                Integer len = null;
                if (StringUtils.isNotEmpty(item.getPathCode())) {
                    len = item.getPathCode().length() + 1;
                }
                dto.setPathCode(item.getPathCode());
                dto.setNewPathCode(curOrgPathCode);
                dto.setLen(len);
                dto.setSort(orgCount[0]);
                finalList.add(dto);
//                cscpOrgRepository.updatePathCode(item.getPathCode(), curOrgPathCode,len);
                orgCount[0]++;
            });
            if(list!=null && list.size()>0){
                list = list.stream().sorted(Comparator.comparing(cscpOrgUpdateCodeDTO::getSort).reversed()).collect(Collectors.toList());
                list.forEach(item->{
                    if (null != item.getLen()) {
                        cscpOrgRepository.updatePathCode(item.getPathCode(), item.getNewPathCode(),item.getLen());
                        //机构编码同步到用户机构中间表
                        cscpUserOrgRepository.updateByPathCode(item.getPathCode(), item.getNewPathCode(),item.getLen());
                    }
                });
            }
        }
    }


    //增加用户机构信息
    public void saveUserOrgInfo(CscpOrgParamDTO cscpOrgParamDTO, Long cscpOrgId) {
        log.info("CscpOrgServiceImpl.saveUserOrgInfo CscpOrgParamDTO = {},Long={}", cscpOrgParamDTO, cscpOrgId);
        //1.首先删除数据库中所有 org_id = cscpOrgId 的数据
        LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpUserOrg::getOrgId, cscpOrgId);
        if (cscpUserOrgRepository.selectList(queryWrapper).size() > 0) {
            cscpUserOrgRepository.delete(queryWrapper);
        }
        //2.增加cscpUserOrgDTOs机构用户信息 （可能存在有多条数据）
       /* List<CscpUserOrgDTO> cscpUserOrgDTOS = cscpOrgParamDTO.getCscpUserOrgDTOs();
        int cscpUserOrgDTOSLength = cscpUserOrgDTOS.size();
        if (cscpUserOrgDTOSLength > 0) {
            for (int i = 0; i < cscpUserOrgDTOSLength; i++) {
                Long cscpUserOrgIid = cscpUserOrgDTOS.get(i).getId();
                CscpUserOrg cscpUserOrg = new CscpUserOrg();
                BeanUtils.copyProperties(cscpUserOrgDTOS.get(i), cscpUserOrg);
                cscpUserOrg.setOrgId(cscpOrgId);
                cscpUserOrgRepository.insert(cscpUserOrg);
            }
        }*/
    }

    //增加机构工作组信息
    public void saveOrgWorkGroupInfo(CscpOrgParamDTO cscpOrgParamDTO, Long cscpOrgId) {
        log.info("CscpOrgServiceImpl.saveOrgWorkGroupInfo CscpOrgParamDTO = {} Long={}", cscpOrgParamDTO, cscpOrgId);
        //1.删除所有org_workgroup中org_id = cscpOrgId 的数据
        LambdaQueryWrapper<CscpWorkGroupOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpWorkGroupOrg::getOrgId, cscpOrgId);
        if (cscpWorkGroupOrgRepository.selectList(queryWrapper).size() > 0) {
            cscpWorkGroupOrgRepository.delete(queryWrapper);
        }
        //机构工作组关联数据增加 （可能存在有多条数据）
       /* List<CscpWorkGroupOrgDTO> cscpWorkGroupOrgDTOS = cscpOrgParamDTO.getCscpWorkGroupOrgDTOs();
        int cscpWorkGroupOrgDTOSLength = cscpWorkGroupOrgDTOS.size();
        if (cscpWorkGroupOrgDTOSLength > 0) {
            for (int i = 0; i < cscpWorkGroupOrgDTOSLength; i++) {
                Long cscpWorkGroupOrgLd = cscpWorkGroupOrgDTOS.get(i).getId();
                CscpWorkGroupOrg inWorkGroupOrg = new CscpWorkGroupOrg();
                BeanUtils.copyProperties(cscpWorkGroupOrgDTOS.get(i), inWorkGroupOrg);
                inWorkGroupOrg.setOrgId(cscpOrgId);
                cscpWorkGroupOrgRepository.insert(inWorkGroupOrg);
            }
        }*/

    }


    //异步加载    把所有的组织机构和相关的工作组、人员查出来
    @Override
    public CscpOrgParamDTO fetchCscpOrgsUpdate(Long parentId) {
        log.info("CscpOrgServiceImpl.fetchCscpOrgsUpdate Long = {}", parentId);
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        if (parentId == null) {
            lambdaQueryWrapper.eq(CscpOrg::getParentId, (long) 0);
        } else {
            lambdaQueryWrapper.eq(CscpOrg::getParentId, parentId);
        }
        List<CscpOrg> orgs = cscpOrgRepository.selectList(lambdaQueryWrapper);

        // list 筛选
        LambdaQueryWrapper<CscpUserOrg> queryWrapper = new LambdaQueryWrapper();
        List<CscpUserOrg> userOrgs = cscpUserOrgRepository.selectList(queryWrapper);

        LambdaQueryWrapper<CscpWorkGroupOrg> queryWorkGroupOrgWrapper = new LambdaQueryWrapper();
        List<CscpWorkGroupOrg> workGroupOrgs = cscpWorkGroupOrgRepository.selectList(queryWorkGroupOrgWrapper);

        List<Long> orgIdList = new ArrayList<>();
        for (CscpOrg org : orgs) {
            orgIdList.add(org.getId());
        }
        //lamda表达式筛选user_orgs.org_id = orgs.id and orgs.parent_id = parent_id
        userOrgs.stream()
                .filter((CscpUserOrg userOrg) -> orgIdList.contains(userOrg.getOrgId()))
                .collect(Collectors.toList());
        //lamda表达式筛选 workGroup_orgs.org_id = orgs.id and orgs.parent_id = parent_id
        workGroupOrgs.stream()
                .filter((CscpWorkGroupOrg workGroupOrg) -> orgIdList.contains(workGroupOrg.getOrgId()))
                .collect(Collectors.toList());
        CscpOrgParamDTO paramDto = new CscpOrgParamDTO();
        paramDto.setCscpOrgDTOs(ListCopyUtil.copy(orgs, CscpOrgDTO.class));
       /* paramDto.setCscpUserOrgDTOs(ListCopyUtil.copy(user, CscpUserOrgDTO.class));
        paramDto.setCscpWorkGroupOrgDTOs(
                ListCopyUtil.copy(workGroup, CscpWorkGroupOrgDTO.class));*/
        return paramDto;
    }


    @Override
    public List<CscpOrg> selectRoleslist(Long id) {
        log.info("CscpOrgServiceImpl.selectRoleslist Long = {}", id);
        List<CscpOrg> cscpOrgs = cscpOrgRepository.selectorgList(id);
        List<CscpOrg> collect = cscpOrgs.stream().filter(i -> {
            return i.getParentId() == 0 || MenuUtils.screen(i, cscpOrgs);
        }).map(i -> {
            i.setChildren(MenuUtils.getorgList(i, cscpOrgs));
            return i;
        }).collect(Collectors.toList());
        return collect;
    }


    @Override
    @Transactional
    // todo 缓存机构 @CacheEvict(value = "orgCache-treeNodes", key = "#id")
    public Long deleteOrgById(Long id) {
        log.info("CscpOrgServiceImpl.deleteOrgById Long = {}", id);
        //判断该机构是否存在下级单位
        CscpOrgDTO cscpOrgDTO = new CscpOrgDTO();
        cscpOrgDTO.setParentId(id);
        List<CscpOrg> cscpOrgList = this.criteriaQueryOrg(cscpOrgDTO);
        if (CollectionUtils.isNotEmpty(cscpOrgList)) {
            throw new BusinessException("删除失败：该机构存在下级单位！");
        }

        //判断该机构是否存在用户
        LambdaQueryWrapper<CscpUserOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpUserOrg::getOrgId, id);
        List<CscpUserOrg> cscpUserOrgList = cscpUserOrgRepository.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isNotEmpty(cscpUserOrgList)) {
            Set<Long> userIds = cscpUserOrgList.stream().map(CscpUserOrg::getUserId).collect(Collectors.toSet());
            List<CscpUserDTO> users = cscpUserRepository.selectListUserIsNotDisable(new ArrayList<>(userIds));
            if (CollectionUtils.isNotEmpty(users)) {
                throw new BusinessException("删除失败：该机构存在关联的用户！");
            }
        }
        //删除机构
        CscpOrg org = cscpOrgRepository.selectById(id);
        this.deleteOrgSort(org);
//        this.deleteSetPathCode(org);
        cscpOrgRepository.deleteById(id);
        addOrgAuditRemove(org);
        //删除角色对应的机构id

        try {
            // 设置缓存的 key，使用 Node 的 ID
            String cacheKey = "treeNode:" + id;
//            String cacheKeyAll = "treeNode:All" + 0;
            redisUtil.del(cacheKey);
//            redisUtil.del(cacheKeyAll);
            this.clearCache(0L);
        }catch (Exception e){
            log.error("更新treeNode缓存失败");
        }
        return org.getId();
    }
    /**
     * 当前父节点下排序号重置
     * @param cscpOrgDTO
     * @return
     */
    private void deleteOrgSort(CscpOrg cscpOrgDTO) {
        // 先查询当前父节点下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
        lambdaQueryWrapper.gt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        int count = cscpOrgRepository.selectCount(lambdaQueryWrapper);

        // 如果存在，则把当前父节点下的大于等于该排序的所有单位排序号 + 1
        if (count > 0) {
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ne(CscpOrg::getId, cscpOrgDTO.getId());
            queryWrapper.gt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            List<CscpOrg> list = cscpOrgRepository.selectListNoAdd(queryWrapper);
            list.forEach(item -> {
                item.setOrderBy(item.getOrderBy() - 1);
                cscpOrgRepository.updateById(item);
            });
        }
    }

    private void deleteSetPathCode(CscpOrg cscpOrgDTO) {
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.gt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
        int count = cscpOrgRepository.selectCountNoAdd(lambdaQueryWrapper);

        // 如果存在，则把大于等于该排序的所有单位编码+1
        if (count > 0) {
            CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrgDTO.getParentId());
            //获取小于当前机构排序号的数量
            LambdaQueryWrapper<CscpOrg> queryCountWrapper = Wrappers.lambdaQuery();
            queryCountWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryCountWrapper.lt(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            final Integer[] orgCount = {cscpOrgRepository.selectCountNoAdd(queryCountWrapper)};
            orgCount[0] = orgCount[0];

            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ne(CscpOrg::getId, cscpOrgDTO.getId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            List<CscpOrg> cscpOrg = cscpOrgRepository.selectListNoAdd(queryWrapper);
            List<cscpOrgUpdateCodeDTO> list = new ArrayList<>();
            List<cscpOrgUpdateCodeDTO> finalList = list;
            try {
                cscpOrg.forEach(item -> {
                    cscpOrgUpdateCodeDTO dto = new cscpOrgUpdateCodeDTO();
                    String curOrgPathCode = "";
                    if (parentOrg == null) {
                        curOrgPathCode = com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgCount[0] + 1),
                                ORG_CODE_LEN);
                    } else {
                        curOrgPathCode = parentOrg.getPathCode() + com.ctsi.hndx.utils.StringUtils.addZeroForNum(String.valueOf(orgCount[0] + 1),
                                ORG_CODE_LEN);
                    }
                    int len = item.getPathCode().length() + 1;
                    dto.setPathCode(item.getPathCode());
                    dto.setNewPathCode(curOrgPathCode);
                    dto.setLen(len);
                    dto.setSort(orgCount[0]);
                    finalList.add(dto);
                    orgCount[0]++;
                });
            } catch (Exception e) {
                log.error("<生成新的pathcode错误>", e);
            }
            if(list!=null && list.size()>0){
//                list = list.stream().sorted(Comparator.comparing(cscpOrgUpdateCodeDTO::getSort).reversed()).collect(Collectors.toList());
                list.forEach(item->{
                    cscpOrgRepository.updatePathCode(item.getPathCode(), item.getNewPathCode(),item.getLen());
                });
            }
        }
    }

    /**
     * 系统管理员删除机构，直接往机构审核表中插入一条审核成功的记录
     * @param org
     */
    private void addOrgAuditRemove(CscpOrg org) {
        if (SecurityUtils.isSystemName()) {
            CscpOrgAuditRemove auditRemove = new CscpOrgAuditRemove();
            auditRemove.setOrgId(org.getId());
            auditRemove.setOrgName(org.getOrgName());
            auditRemove.setOrgCode(org.getOrgCode());

            auditRemove.setAuditCreateName(SecurityUtils.getCurrentRealName());
            auditRemove.setAuditExplain("由系统管理员直接移除组织机构");
            auditRemove.setAuditType(1);
            auditRemove.setRemoveType(0);
            cscpOrgAuditRemoveRepository.insert(auditRemove);
        }
    }

    @Override
    public List<Long> listQryParentOrgId(Long id) {
        log.info("CscpOrgServiceImpl.listQryParentOrgId Long = {}", id);
        CscpOrgDTO cscpOrgDTO = this.findOne(id);
        if (Objects.isNull(cscpOrgDTO)) {
            return null;
        }
        List<Long> parentOrgIdList = new ArrayList<>();

        while (!ROOT_ORG_ID.equals(cscpOrgDTO.getParentId())) {
            Long parentOrgId = cscpOrgDTO.getParentId();
            parentOrgIdList.add(parentOrgId);
            cscpOrgDTO = this.findOne(parentOrgId);
        }
        Collections.reverse(parentOrgIdList);
        parentOrgIdList.add(id);
        return parentOrgIdList;
    }

    @Override
    public List<CscpOrgDTO> criteriaQueryOrgDTO(CscpOrgDTO orgDTO) {
        log.info("CscpOrgServiceImpl.criteriaQueryOrgDTO CscpOrgDTO = {}", orgDTO);
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        // 这里是需要查询名称完全相同的组织机构必须使用eq
        if (org.apache.commons.lang3.StringUtils.isNotBlank(orgDTO.getOrgName())) {
            queryWrapper.eq(CscpOrg::getOrgName, orgDTO.getOrgName());
        }
        if (orgDTO.getType() != null) {
            queryWrapper.eq(CscpOrg::getType, orgDTO.getType());
        }
        if (Objects.nonNull(orgDTO.getCompanyId())) {
            queryWrapper.eq(CscpOrg::getParentId, orgDTO.getCompanyId());
        }
        if (orgDTO != null &&
                StringUtils.isEmpty(orgDTO.getOrgName()) &&
                orgDTO.getType() == null &&
                orgDTO.getCompanyId() == null) {

            return Collections.emptyList();
        }


        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        return ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
    }

    @Override
    public List<CscpOrgDTO> criteriaQueryOrgDTONew(CscpOrgDTO orgDTO, String flag, int size) {
        log.info("CscpOrgServiceImpl.criteriaQueryOrgDTO CscpOrgDTO = {}", orgDTO);
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();

        if ("KDW".equals(flag) || "QH".equals(flag)) {
            // 当跨单位且多个部门的场景下 SQL条件拼接方式
            if (size > 1) {
                if (StringUtils.isNotEmpty(orgDTO.getOrgName())) {
                    queryWrapper.eq(CscpOrg::getOrgName, orgDTO.getOrgName());
                }
                if (orgDTO.getType() != null) {
                    queryWrapper.eq(CscpOrg::getType, orgDTO.getType());
                }
                if (Objects.nonNull(orgDTO.getCompanyId())) {
                    queryWrapper.eq(CscpOrg::getCompanyId, orgDTO.getCompanyId());
                }
            } else {
                // 跨单位单个部门
                if (orgDTO.getType() != null) {
                    queryWrapper.eq(CscpOrg::getType, orgDTO.getType());
                }
                if (Objects.nonNull(orgDTO.getCompanyId())) {
                    queryWrapper.eq(CscpOrg::getId, orgDTO.getCompanyId());
                }
            }
            if (orgDTO != null &&
                    StringUtils.isEmpty(orgDTO.getOrgName()) &&
                    orgDTO.getType() == null &&
                    orgDTO.getCompanyId() == null) {

                return Collections.emptyList();
            }
        }

        if ("DW".equals(flag)) {
            // 这里是需要查询名称完全相同的组织机构必须使用eq
            if (StringUtils.isNotEmpty(orgDTO.getOrgName())) {
                queryWrapper.eq(CscpOrg::getOrgName, orgDTO.getOrgName());
            }
            if (orgDTO.getType() != null) {
                queryWrapper.eq(CscpOrg::getType, orgDTO.getType());
            }
            if (Objects.nonNull(orgDTO.getCompanyId())) {
                queryWrapper.eq(CscpOrg::getCompanyId, orgDTO.getCompanyId());
            }
            if (orgDTO != null &&
                    StringUtils.isEmpty(orgDTO.getOrgName()) &&
                    orgDTO.getType() == null &&
                    orgDTO.getCompanyId() == null) {

                return Collections.emptyList();
            }
        }

        if ("QH".equals(flag)) {
            List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
            return ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
        }
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectList(queryWrapper);
        return ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
    }

    @Override
    public List<Node<CscpOrgDTO>> selectCheckedOrgNodeTree(Long companyId, List<Long> ids) {
        log.info("CscpOrgServiceImpl.selectCheckedOrgNodeTree Long = {} List={}", companyId, ids);
        Set<Long> set = this.listBatchCheckedOrgId(ids);
        // 因为所有ids都在一个单位下，所有companyId都相同
        if (!set.isEmpty() && companyId == 0) {
            CscpOrg cscpOrg = cscpOrgRepository.selectById(ids.get(0));
            companyId = cscpOrg.getCompanyId();
        }
        /*if (Objects.isNull(companyId) )  {

        }*/
        return this.listCheckedOrgNodeTree(companyId, ids, set);
    }

    private Set<Long> listBatchCheckedOrgId(List<Long> idList) {
        log.info("CscpOrgServiceImpl.listBatchCheckedOrgId Long = {}", idList);
        Set<Long> checkedOrgIds = new HashSet<>();
        for (Long id : idList) {
            Set<Long> set = this.listCheckedOrgId(id);
            if (CollectionUtil.isNotEmpty(set)) {
                checkedOrgIds.addAll(set);
            }
        }
        return checkedOrgIds;
    }


    private Set<Long> listCheckedOrgId(Long id) {
        CscpOrg cscpOrg = cscpOrgRepository.selectById(id);
        if (Objects.isNull(cscpOrg)) {
            return null;
        }
        if (Objects.isNull(cscpOrg.getCompanyId())) {
            throw new BusinessException("ID为: " + id + " 的组织机构的单位ID为空");
        }

        Set<Long> set = new HashSet<>();
        while (!ROOT_ORG_ID.equals(cscpOrg.getParentId())) {
            Long parentOrgId = cscpOrg.getParentId();
            set.add(parentOrgId);
            cscpOrg = cscpOrgRepository.selectById(parentOrgId);
        }
        set.add(id);
        return set;
    }


    private List<CscpOrg> criteriaQueryOrg(CscpOrgDTO dto) {
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        if (dto.getId() != null) {
            queryWrapper.eq(CscpOrg::getId, dto.getId());
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(dto.getOrgName())) {
            queryWrapper.like(CscpOrg::getOrgName, dto.getOrgName());
        }
        if (dto.getParentId() != null) {
            queryWrapper.eq(CscpOrg::getParentId, dto.getParentId());
        }
        if (dto.getType() != null) {
            queryWrapper.eq(CscpOrg::getType, dto.getType());
        }
        if (Objects.nonNull(dto.getCompanyId())) {
            queryWrapper.eq(CscpOrg::getCompanyId, dto.getCompanyId());
        }
        return cscpOrgRepository.selectListNoAdd(queryWrapper);
    }

    /**
     * 获取机构层级
     * @param parentId
     * @return
     */
    private Integer getOrgLevel(Long parentId) {
        if (ROOT_ORG_ID.equals(parentId)) {
            return ROOT_ORG_LEVEL;
        }
        CscpOrg parentCscpOrg = cscpOrgRepository.selectById(parentId);
        return parentCscpOrg.getLevel() + 1;
    }

    /**
     * 当前父节点下排序号重置
     * @param cscpOrgDTO
     * @return
     */
    private void updateOrgSort(CscpOrgDTO cscpOrgDTO) {
        // 先查询当前父节点下该排序号是否存在
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
//        lambdaQueryWrapper.eq(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_2.getCode());
        lambdaQueryWrapper.eq(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
        lambdaQueryWrapper.ne(CscpOrg::getId, cscpOrgDTO.getId());
        int count = cscpOrgRepository.selectCount(lambdaQueryWrapper);

        // 如果存在，则把当前父节点下的大于等于该排序的所有单位排序号 + 1
        if (count > 0) {
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpOrg::getParentId, cscpOrgDTO.getParentId());
            queryWrapper.ne(CscpOrg::getId, cscpOrgDTO.getId());
            queryWrapper.ge(CscpOrg::getOrderBy, cscpOrgDTO.getOrderBy());
            queryWrapper.orderByAsc(CscpOrg::getOrderBy);
            queryWrapper.orderByAsc(CscpOrg::getCreateTime);
            List<CscpOrg> list = cscpOrgRepository.selectList(queryWrapper);
            list.forEach(item -> {
                item.setOrderBy(item.getOrderBy() + 1);
                cscpOrgRepository.updateById(item);
            });
        }
    }

    /**
     * 获取组织机构下面的用户
     *
     * @param id
     * @return
     */
    @Override
    public List<CscpUserDTO> selectUserlist(Long id) {
        log.info("CscpOrgServiceImpl.selectUserlist Long = {}", id);

        //鉴权登录用户有没有权限查询
//        if(checkUserPermissions(id)==false){
//            log.info(SecurityUtils.getCurrentUserId()+"用户没有权限查询"+id);
//            return Collections.emptyList();
//        }

        CscpUserDTO cscpUserDTO = new CscpUserDTO();

//        cscpUserDTO.setDepartmentId(id);
        cscpUserDTO.setCompanyId(id);
        //用户信息
        List<CscpUserDTO> userList = cscpUserRepository.selectUserByCompanyId(cscpUserDTO);
        //如果通过单位ID查不到数据，就用部门ID查询一次
        if(null!=userList&&userList.size()<=0){
            cscpUserDTO.setDepartmentId(id);
            cscpUserDTO.setCompanyId(null);
            //用户信息
            userList = cscpUserRepository.selectUserByCompanyId(cscpUserDTO);
        }
        //给所有用户添加对应的单位和部门
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        userList.stream().forEach(i -> {
            i.setCompanyId(currentCscpUserDetail.getCompanyId());
            i.setCompanyName(currentCscpUserDetail.getCompanyName());
        });


        return userList;
    }

    @Override
    public void createCompanyAdmin(String orgCode) {
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrg::getType,2);
        queryWrapper.like(CscpOrg::getOrgCode, orgCode);
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        if(cscpOrgList != null && !cscpOrgList.isEmpty()){
            for (CscpOrg cscpOrg : cscpOrgList){
                CscpOrgDTO orgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
                CscpUserDTO cscpUserDTO = new CscpUserDTO();
                String orgName = orgDTO.getOrgName();
                String name = PinyinUtil.getFirstLetter(orgName, "");
                String newName = cscpUserService.getUserNameMaxNumber(name + "admin");
                cscpUserDTO.setLoginName(newName);
                cscpUserDTO.setRealName(orgName + "管理员");
                cscpUserDTO.setMobile(SysConstant.DELAULT_MOBILE);
                if (orgDTO.getTenantId() != null) {
                    cscpUserDTO.setTenantId(orgDTO.getTenantId());
                }
                List<Long> roleIds = new ArrayList();
                roleIds.add(Long.valueOf(SystemRole.COMPANY_ROLE.getId()));
                cscpUserDTO.setRoleIds(roleIds);

                List<Long> orgIds = new ArrayList<>();
                orgIds.add(orgDTO.getId());
                cscpUserDTO.setOrgIdList(orgIds);
                cscpUserDTO.setCrmTenantType(orgDTO.getCrmTenantType());
                cscpUserDTO.setGroupNumber(orgDTO.getGroupNumber());
                cscpUserDTO.setOrderBy(CscpUserDTO.DEFAULT_USER_SORT);
                cscpUserDTO.setStatistics(false);
                cscpUserDTO.setDisplay(false);
                cscpUserDTO.setSecurityClassificationCode("0");
                cscpUserDTO.setSecurityClassificationCodeName("一般");
                cscpUserDTO.setExamineStatus(1);
                cscpUserService.insert(cscpUserDTO);
            }
        }
    }

    @Override
    public void createCompanyAdmin2(String orgCodePath) {
        if (StringUtils.isEmpty(orgCodePath)) {
            return;
        }
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrg::getType,2);
        queryWrapper.like(CscpOrg::getOrgCodePath, orgCodePath);
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        if(cscpOrgList != null && !cscpOrgList.isEmpty()){
            for (CscpOrg cscpOrg : cscpOrgList){
                CscpOrgDTO orgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
                CscpUserDTO cscpUserDTO = new CscpUserDTO();
                String orgName = orgDTO.getOrgName();
                String name = PinyinUtil.getFirstLetter(orgName, "");
                String newName = cscpUserService.getUserNameMaxNumber(name + "admin");
                cscpUserDTO.setLoginName(newName);
                cscpUserDTO.setRealName(orgName + "管理员");
                cscpUserDTO.setMobile(SysConstant.DELAULT_MOBILE);
                if (orgDTO.getTenantId() != null) {
                    cscpUserDTO.setTenantId(orgDTO.getTenantId());
                }
                List<Long> roleIds = new ArrayList();
                roleIds.add(Long.valueOf(SystemRole.COMPANY_ROLE.getId()));
                cscpUserDTO.setRoleIds(roleIds);

                List<Long> orgIds = new ArrayList<>();
                orgIds.add(orgDTO.getId());
                cscpUserDTO.setOrgIdList(orgIds);
                cscpUserDTO.setCrmTenantType(orgDTO.getCrmTenantType());
                cscpUserDTO.setGroupNumber(orgDTO.getGroupNumber());
                cscpUserDTO.setOrderBy(CscpUserDTO.DEFAULT_USER_SORT);
                cscpUserDTO.setStatistics(false);
                cscpUserDTO.setDisplay(false);
                cscpUserDTO.setSecurityClassificationCode("0");
                cscpUserDTO.setSecurityClassificationCodeName("一般");
                cscpUserDTO.setExamineStatus(1);
                cscpUserService.insert(cscpUserDTO);
            }
        }
    }


    /**
     * 获根据单位id获取单位下面的所有用户
     *
     * @return
     */
    @Override
    public List<CscpUserDTO> selectCompayAllUserByCompanyId(Long companyId) {
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        if (companyId == null) {
            cscpUserDTO.setCompanyId(SecurityUtils.getCurrentCompanyId());
        } else {
            cscpUserDTO.setCompanyId(companyId);

        }

        //用户信息
        List<CscpUserDTO> userList = cscpUserRepository.selectUserByCompanyId(cscpUserDTO);

        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        //给所有用户添加对应的单位和部门
        userList.stream().forEach(i -> {
            i.setCompanyName(currentCscpUserDetail.getCompanyName());
        });


        return userList;
    }

    /**
     * 根据单位id获取单位下面的所有用户的单位id、单位名称、部门id、部门名称
     *
     * @param companyId 单位id
     * @return CscpUserDTO
     */
    @Override
    public List<CscpUserDTO> selectUserDetailByCompanyId(Long companyId) {
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        if (companyId == null) {
            throw new BusinessException("单位id不能为空");
        } else {
            cscpUserDTO.setCompanyId(companyId);
        }

        // 返回用户信息，包含单位id、单位名称、部门id、部门名称
        return cscpUserRepository.selectUserDetailByCompanyId(cscpUserDTO);
    }

    @Override
    public PageResult<CscpUserDTO> pageQueryUserList(Long id, String realName, List<Long> userIds, BasePageForm basePageForm) {
        log.info("CscpOrgServiceImpl.pageQueryUserList Long = {}", id);

        if (org.apache.commons.lang3.StringUtils.isNotBlank(realName)) {
            realName = this.division(realName);
        }

        IPage<CscpUserDTO> cscpUserIPage =
                cscpUserRepository.pageQueryUserList(PageHelperUtil.getMPlusPageByBasePage(basePageForm), id, realName, userIds);
        List<CscpUserDTO> data = cscpUserIPage.getRecords();


//        if (!data.isEmpty()) {
//            //查询对应的部门和单位信息
//            //部门
//            CscpOrg org = cscpOrgRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, id));
//            //单位信息
//            CscpOrg company = cscpOrgRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, data.get(0).getCompanyId()));
//
//            //给所有用户添加对应的单位和部门
//            data.forEach(i -> {
//                if (!Objects.isNull(company)) {
//                    i.setCompanyName(company.getOrgName());
//                    i.setCompanyId(company.getId());
//                }
//                if (!Objects.isNull(org) && org.getId().longValue() != company.getId().longValue()) {
//                    i.setDepartmentName(org.getOrgName());
//                    i.setDepartmentId(org.getId());
//                } else {
//                    i.setDepartmentId(null);
//                }
//            });
//        }

        return new PageResult<>(data, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
    }


    @Override
    public PageResult<CscpUserDTO> pageSelectUsers(Long id, String realName, BasePageForm basePageForm) {
        log.info("CscpOrgServiceImpl.pageSelectUsers id = {}", id);

        if (org.apache.commons.lang3.StringUtils.isNotBlank(realName)) {
            realName = this.division(realName);
        }

        IPage<CscpUserDTO> cscpUserIPage =
                cscpUserRepository.pageSelectUsers(PageHelperUtil.getMPlusPageByBasePage(basePageForm), id, realName);
        List<CscpUserDTO> data = cscpUserIPage.getRecords();


        if (!data.isEmpty()) {
            //查询对应的部门和单位信息
            //部门
            CscpOrg org = cscpOrgRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, id));
            //单位信息
            CscpOrg company = cscpOrgRepository.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, data.get(0).getCompanyId()));

            //给所有用户添加对应的单位和部门
            data.forEach(i -> {
                if (!Objects.isNull(company)) {
                    i.setCompanyName(company.getOrgName());
                    i.setCompanyId(company.getId());
                }
                if (!Objects.isNull(org)) {
                    if (company == null || (org.getId().longValue() != company.getId().longValue())) {
                        i.setDepartmentName(org.getOrgName());
                        i.setDepartmentId(org.getId());
                    }
                } else {
                    i.setDepartmentId(null);
                }
            });
        }

        return new PageResult<>(data, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
    }

    @Override
    public List<CscpOrgDTO> selectChildrenListParentId(Long parentId) {
        log.info("CscpOrgServiceImpl.selectChildrenListParentId Long = {}", parentId);
        List<Node<CscpOrgDTO>> nodes = super.selectChildrenListNodeByParentId(parentId);
        List<CscpOrgDTO> list = new ArrayList<>();
        for (Node<CscpOrgDTO> n : nodes) {
            list.add(n.getDetailsData());
            if (!n.getChildren().isEmpty()) {
                getCscpOrgDTO(n, list);
            }
        }
        list.removeAll(Collections.singleton(null));
        return list;
    }

    @Override
    public List<CscpOrgDTO> selectLabelParentOrg(List<Long> labelId) {
        if (labelId.isEmpty()) {
            return new LinkedList<>();
        }
       //log.info("CscpOrgServiceImpl.selectLabelParentOrg List = {}", labelId);
        List<CscpOrgDTO> list = new ArrayList<>();
        List<Future< List<CscpOrgDTO> >> futures = new ArrayList<>();
        // 2. 创建线程池（根据CPU核心数优化）
        ExecutorService executor = Executors.newFixedThreadPool(
                Runtime.getRuntime().availableProcessors() // 可根据需要调整线程池大小
        );
        int BATCH_SIZE = 2000;
        if (labelId.size() > BATCH_SIZE){
            for (int i = 0; i < labelId.size(); i+= BATCH_SIZE) {
                List<Long> labelIds = labelId.subList(i, Math.min(i + BATCH_SIZE, labelId.size()));
                futures.add(executor.submit(() ->
                        cscpOrgRepository.selectListNoAdd
                                (new LambdaQueryWrapper<CscpOrg>().in(CscpOrg::getId, labelIds))
                                .stream().map(t -> BeanConvertUtils.copyProperties(t, CscpOrgDTO.class))
                                .collect(Collectors.toList())));
            }
            // 合并结果（阻塞等待）
            // 4. 获取所有结果（阻塞等待）
            for (Future<List<CscpOrgDTO>> future : futures) {
                try {
                    list.addAll(future.get());
                } catch (InterruptedException e) {
                    e.printStackTrace();
                } catch (ExecutionException e) {
                    e.printStackTrace();
                }
            }
            // 5. 关闭线程池
            executor.shutdown();
        }else{
            //查询标签对应的机构信息
            list = cscpOrgRepository.selectListNoAdd
                    (new LambdaQueryWrapper<CscpOrg>().in(CscpOrg::getId, labelId))
                    .stream().map(i -> BeanConvertUtils.copyProperties(i, CscpOrgDTO.class))
                    .collect(Collectors.toList());
        }


        //获取机构的子节点
        List<CscpOrgDTO> listRemove = new LinkedList<>();
        for (int i = 0; i < list.size(); i++) {
            for (int y = 0; y < list.size(); y++) {
                if (list.get(i).getParentId().longValue() == list.get(y).getId().longValue()) {
                    listRemove.add(list.get(i));
                    break;
                }
            }
        }

        //将标签对应机构下的子节点去除
        List<CscpOrgDTO> finalList = list;
        listRemove.stream().forEach(i -> finalList.remove(i));

        return finalList;
    }

    @Override
    public List<CscpOrgDTO> selectLabelOrg(List<Long> orgId) {
        log.info("CscpOrgServiceImpl.selectLabelOrg List = {}", orgId);
        List<CscpOrgDTO> list = new LinkedList<>();
        if (orgId.isEmpty()) {
            return list;
        }
        orgId.removeAll(Collections.singleton(null));
        if (null != orgId) {
            list = cscpOrgRepository.selectListNoAdd
                            (new LambdaQueryWrapper<CscpOrg>().in(CscpOrg::getId, orgId).orderByAsc(CscpOrg::getOrderBy))
                    .stream().map(i -> BeanConvertUtils.copyProperties(i, CscpOrgDTO.class)).collect(Collectors.toList());
        }
        return list;
    }

    @Override
    public List<TLabelOrg> selectParentIdOrg(Long orgId, Long labelId) {
        //log.info("CscpOrgServiceImpl.selectParentIdOrg Long = {} Long = {}", orgId, labelId);
        List<TLabelOrg> collect = cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getParentId, orgId))
                .stream().map(i -> {
                    //查询子机构
                    TLabelOrg tLabelOrg = tLabelOrgMapperl.selectOneNoAdd(new LambdaQueryWrapper<TLabelOrg>()
                            .eq(labelId!=null, TLabelOrg::getDisplayRangeId, labelId)
                            .orderByAsc(TLabelOrg::getSort)
                            .eq(TLabelOrg::getOrgId, i.getId()));
                    //判断该子机构是否在指定标签下
                    if(!Objects.isNull(tLabelOrg)){
                        tLabelOrg.setSort(null);
                    }
                    return !Objects.isNull(tLabelOrg) ? tLabelOrg : null;
                }).collect(Collectors.toList());
        collect.removeAll(Collections.singleton(null));
        return collect;
    }

    @Override
    public Map<Long, TLabelOrg> selectBatchParentIdOrg(List<Long> orgIds, Long labelId) {
        if (CollUtil.isEmpty(orgIds)) {
            return Collections.emptyMap();
        }

        try {
            // 查询子机构和标签关联
            List<TLabelOrg> labelOrgs = tLabelOrgMapperl.selectList(
                    new LambdaQueryWrapper<TLabelOrg>()
                            .in(TLabelOrg::getOrgId, orgIds)
                            .eq(labelId != null, TLabelOrg::getDisplayRangeId, labelId)
                            .orderByAsc(TLabelOrg::getSort)
            );

            // 当没有关联数据时返回空Map
            if (CollUtil.isEmpty(labelOrgs)) {
                return Collections.emptyMap();
            }

            // 构建orgId到TLabelOrg的映射
            return labelOrgs.stream()
                    .peek(org -> org.setSort(null))
                    .collect(Collectors.toMap(TLabelOrg::getOrgId,Function.identity(),
                            (existing, replacement) -> existing  // 如果有重复，保留第一个
                    ));
        } catch (Exception e) {
            log.error("批量查询机构关联数据失败: orgIds={}", orgIds, e);
            throw new CompletionException(e);
        }
    }

    @Override
    public Boolean selectSubOrgExistFlag(Long orgId, Long labelId) {
        Integer count = cscpOrgRepository.selectCountNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getParentId, orgId));

        return count>0?true:false;
    }

    @Override
    public Integer selectSubDeptOrgCount(Long orgId) {
        List<CscpOrg> deptOrgList= cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>()
                .select(CscpOrg::getId).eq(CscpOrg::getParentId, orgId));
        if (CollectionUtils.isEmpty(deptOrgList)) {
            return 0;
        }
        Integer personCount = cscpUserOrgRepository.selectCountNoAdd(new LambdaQueryWrapper<CscpUserOrg>()
                .in(CscpUserOrg::getOrgId, deptOrgList.stream().map(CscpOrg::getId).collect(Collectors.toList())));

        return personCount;
    }

    @Override
    public List<CscpOrgDTO> selectSubOrgList(Long parentId, Executor executor) {
        log.info("APP根据id = {} 查询展开单位下机构", parentId);

        try {
            // 使用单次数据库查询获取所有需要的数据
            return CompletableFuture
                    .supplyAsync(() -> cscpOrgRepository.selectSubOrgListWithChildrenStatus(parentId), executor)
                    .exceptionally(throwable -> {
                        log.error("查询下级机构失败: parentId={}", parentId, throwable);
                        return Collections.emptyList();
                    })
                    .join();

        } catch (Exception e) {
            log.error("查询下级机构失败: parentId={}", parentId, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取指定租户下面的所有单位
     *
     * @param id
     * @return
     */
    @Override
    public List<Node<CscpOrgDTO>> selectTenantOrgPage(Long id) {
        log.info("CscpOrgServiceImpl.selectTenantOrgPage Long = {} ", id);
        try {
            longThreadLocal.set(id);

            if (SecurityUtils.isGeneralName()) {
                ORGTYPE1ThreadLocal.set(true);
            }
            // 查询该租户下的所有顶级单位
            List<CscpOrgDTO> cscpOrgDTOList = this.selectRootOrgByTenantId(id);
            List<Node<CscpOrgDTO>> nodeList = new ArrayList<>();
            for (CscpOrgDTO orgDTO : cscpOrgDTOList) {
                Node<CscpOrgDTO> eNode = selectNodeByParentId(orgDTO.getId());
                nodeList.add(eNode);
            }
            return nodeList;
        } finally {
            longThreadLocal.remove();
            ORGTYPE1ThreadLocal.remove();
        }
    }

    /**
     * 查询租户下面的所有组织机构，包括单位、虚拟机构和部门
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<Node<CscpOrgDTO>> selectOrgTreeByTenantId(Long tenantId) {
        log.info("CscpOrgServiceImpl.selectOrgTreeByTenantId Long = {} ", tenantId);
        // 只允许平台管理员才能查看
        if (!SecurityUtils.isSystemName()) {
            throw new BusinessException("只允许平台管理员使用");
        }
        // 查询该租户下的所有顶级单位
        List<CscpOrgDTO> cscpOrgDTOList = this.selectRootOrgByTenantId(tenantId);
        List<Node<CscpOrgDTO>> nodeList = new ArrayList<>();
        for (CscpOrgDTO orgDTO : cscpOrgDTOList) {
            Node<CscpOrgDTO> eNode = selectNodeByParentId(orgDTO.getId());
            nodeList.add(eNode);
        }

        return nodeList;
    }

    /**
     * 查询该租户下所有顶级单位
     *
     * @param tenantId
     * @return
     */
    private List<CscpOrgDTO> selectRootOrgByTenantId(Long tenantId) {
        log.info("CscpOrgServiceImpl.selectRootOrgByTenantId Long = {} ", tenantId);
        if (Objects.isNull(tenantId)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CscpOrg::getTenantId, tenantId);
        queryWrapper.eq(CscpOrg::getParentId, 0);
        queryWrapper.orderByAsc(CscpOrg::getOrderBy);
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        return ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
    }

    @Override
    public List<CscpOrgDTO> selectSubordinateAllOrg(Long parentId, Long tenantId) {
        log.info("CscpOrgServiceImpl.selectSubordinateAllOrg Long = {} Long = {} ", parentId, tenantId);
        //查询下级的机构
        List<CscpOrgDTO> collect = cscpOrgRepository.selectListNoAdd(
                        new LambdaQueryWrapper<CscpOrg>()
                                .eq(CscpOrg::getTenantId, tenantId)
                                .eq(CscpOrg::getParentId, parentId))
                .stream().map(i -> {
                    CscpOrgDTO cscpOrgDTO = BeanConvertUtils.copyProperties(i, CscpOrgDTO.class);
                    cscpOrgDTO.setIsValuable(0);
                    return cscpOrgDTO;
                }).collect(Collectors.toList());

        return collect;
    }


    @Override
    public PageResult<CscpUserDTO> queryTenantUser(String realName, BasePageForm basePageForm) {
        log.info("CscpOrgServiceImpl.queryTenantUser String = {} ", realName);
        //或者当前租户的上下级租户体系
        List<Long> allTenantList = sysTenantService.getAllTenantList(SecurityUtils.getCurrentCscpUserDetail().getTenantId()).stream().map(i -> i.getId()).collect(Collectors.toList());

        //模糊查询用户
        CscpUserDTO build = CscpUserDTO.builder().realName(this.division(realName))
                .status(1).tenantIdList(allTenantList).build();
        IPage<CscpUserDTO> cscpUserIPage = cscpUserRepository.criteriaQueryCscpUserList(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), build);

        List<CscpUserDTO> cscpUserDTOList = cscpUserIPage.getRecords().stream().map(x -> {
            // 查询用户关联的单位信息
            if (Objects.nonNull(x.getCompanyId())) {
                CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(x.getCompanyId());
                if (Objects.nonNull(cscpOrgDTO)) {
                    x.setCompanyName(cscpOrgDTO.getOrgName());
                }
            }
            return x;
        }).collect(Collectors.toList());

        return new PageResult<CscpUserDTO>(cscpUserDTOList, cscpUserIPage.getTotal(), cscpUserIPage.getTotal());
    }


    public CscpOrgDTO getCscpOrgDTO(Node<CscpOrgDTO> n, List list) {
        for (Node<CscpOrgDTO> n1 : n.getChildren()) {
            list.add(n1.getDetailsData());
            if (n1.getChildren()!=null &&!n1.getChildren().isEmpty()) {
                list.add(getCscpOrgDTO(n1, list));
            }
        }
        return null;
    }


    @Override
    public List<CscpOrgDTO> getDataDtOFromDomin(List<CscpOrg> list) {
        List<CscpOrgDTO> cscpOrgDTOList = ListCopyUtil.copy(list, CscpOrgDTO.class);
        if (westoneEncryptService.isCipherMachine()) {
            cscpOrgDTOList.stream().forEach(i -> {
                // TODO 数据完整性校验
                String orgCode = i.getOrgCode();
                if (StringUtils.isNotNull(orgCode)) {
                    String hmacOrgCode = i.getHmacOrgCode();
                    boolean isIntegrity = westoneEncryptService.compareSM3HMAC(i.getOrgName() + i.getOrgCode(), hmacOrgCode);
                    if (isIntegrity) {
                        i.setDataIsIntegrity(0);
                    } else {
                        i.setDataIsIntegrity(1);
                    }
                } else {
                    i.setDataIsIntegrity(0);
                }
            });
        }
        return cscpOrgDTOList;
    }

    @Override
    public CscpOrgDTO copyDto(CscpOrg cscpOrg, CscpOrgDTO treeData) {
        treeData = new CscpOrgDTO();
        BeanUtils.copyProperties(cscpOrg, treeData);
        if (westoneEncryptService.isCipherMachine()) {
            // TODO 数据完整性校验
            String orgCode = treeData.getOrgCode();
            if (StringUtils.isNotNull(orgCode)) {
                String hmacOrgCode = treeData.getHmacOrgCode();
                boolean isIntegrity = westoneEncryptService.compareSM3HMAC(treeData.getOrgName() + treeData.getOrgCode(), hmacOrgCode);
                if (isIntegrity) {
                    treeData.setDataIsIntegrity(0);
                } else {
                    treeData.setDataIsIntegrity(1);
                }
            } else {
                treeData.setDataIsIntegrity(0);
            }
        }
        return treeData;
    }

    /**
     * 根据父节点的id找到子节点
     *
     * @param parentId
     * @return
     */
    @Override
    public List<CscpOrgDTO> selectChildren(Long parentId) {
        log.info("CscpOrgServiceImpl.selectChildren Long = {} ", parentId);
        try {
            Assert.notNull(parentId, "parentId is null");
            QueryWrapper<CscpOrg> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("parent_id", parentId);
            queryWrapper.orderByAsc("order_by");
            Boolean aBoolean = ORGTYPE1ThreadLocal.get();
            //  如果是单位用户，只获取本单位组织机构，过滤虚拟机构，且按照单位过滤，如果是要获取本租户的不过滤
            if (!SecurityUtils.isRegionAdmin() && SecurityUtils.isGeneralName() && aBoolean == null) {
                queryWrapper.eq("company_id", SecurityUtils.getCurrentCompanyId());
                queryWrapper.ne("type", OrgTypeEnum.ORG_TYPE_1.getCode());
            }
            List<CscpOrg> list = null;
            //如果不为空说明查询的是外租户的单位信息
            Long aLong = longThreadLocal.get();
            if (StringUtils.isNotNull(aLong)) {
                list = baseMapper.selectListNoAdd(queryWrapper);
            } else {
                if (parentId == 0) {
                    list = baseMapper.selectList(queryWrapper);
                } else {
                    //
                    list = baseMapper.selectListNoAdd(queryWrapper);
                }
            }
            return getDataDtOFromDomin(list);
        } finally {

        }
    }

    /**
     * 查询单位下所有部门信息(只查和该单位的直接部门或子部门，不会遍历该单位下的虚拟机构和子单位的部门)
     *
     * @param companyId
     * @return
     */
    @Override
    public List<CscpOrgDTO> getAllChildDepartmentList(Long companyId) {
        log.info("CscpOrgServiceImpl.getAllChildDepartmentList Long = {} ", companyId);
        if (Objects.isNull(companyId)) {
            return new ArrayList<>();
        }

        // 存最底层的子部门
        List<CscpOrgDTO> departmentDTOList = new ArrayList<>();
        CscpOrgDTO orgDTO = this.findOne(companyId);
        departmentDTOList.add(orgDTO);

        // 存具有子节点的部门
        LinkedList<CscpOrgDTO> parentDepartmentDTOList = new LinkedList<>();

        List<CscpOrgDTO> childDepartmentDTOList = this.getChildOrgList(companyId, OrgTypeEnum.ORG_TYPE_3.getCode());
//        for (CscpOrgDTO cscpOrgDTO : childDepartmentDTOList) {
//            if (this.hasChildOrg(cscpOrgDTO.getId(), OrgTypeEnum.ORG_TYPE_3.getCode())) {
//                departmentDTOList.add(cscpOrgDTO);
//                parentDepartmentDTOList.add(cscpOrgDTO);
//            } else {
//                departmentDTOList.add(cscpOrgDTO);
//            }
//        }

        // 如果没有具有子节点的租户，则直接返回存最底层的子租户的集合即可
        if (CollectionUtils.isEmpty(parentDepartmentDTOList)) {
            return childDepartmentDTOList;
        }

        // 如果子租户还有子节点，则继续遍历
        while (CollectionUtils.isNotEmpty(parentDepartmentDTOList)) {
            CscpOrgDTO tempDepartmentDTO = parentDepartmentDTOList.removeFirst();
            childDepartmentDTOList = this.getChildOrgList(tempDepartmentDTO.getId(), OrgTypeEnum.ORG_TYPE_3.getCode());
//            for (CscpOrgDTO cscpOrgDTO : childDepartmentDTOList) {
//                if (this.hasChildOrg(cscpOrgDTO.getId(), OrgTypeEnum.ORG_TYPE_3.getCode())) {
//                    departmentDTOList.add(cscpOrgDTO);
//                    parentDepartmentDTOList.add(cscpOrgDTO);
//                } else {
//                    departmentDTOList.add(cscpOrgDTO);
//                }
//            }
        }
        return childDepartmentDTOList;
    }

    @Override
    public List<CscpOrgDTO> getChildOrgList(Long orgId, Integer type) {
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrg::getParentId, orgId);
        if (Objects.nonNull(type)) {
            queryWrapper.eq(CscpOrg::getType, type);
        }
        List<CscpOrg> childOrgList = cscpOrgRepository.selectList(queryWrapper);
        return ListCopyUtil.copy(childOrgList, CscpOrgDTO.class);
    }

    /**
     * 判断是否还有子节点
     *
     * @param orgId
     * @return
     */
    private Boolean hasChildOrg(Long orgId, Integer type) {
        // log.info("CscpOrgServiceImpl.hasChildOrg Long = {} Integer={}", orgId, type);
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrg::getParentId, orgId);
        if (Objects.nonNull(type)) {
            queryWrapper.eq(CscpOrg::getType, type);
        }
        Integer count = cscpOrgRepository.selectCountNoAdd(queryWrapper);
        return count > 0;
    }

    /**
     * 查询本单位所有机构(限普通用户)
     *
     * @param parentId
     * @return
     */
    @Override
    public List<Node<CscpOrgDTO>> seletThisUnitAllDepartment(Long parentId, List<Long> ids) {
        log.info("CscpOrgServicelmpl.seletThisUnitAllDepartment Long = {}", parentId);
        if (Objects.isNull(parentId)) {
            throw new BusinessException("没有找到该用户的单位id");
        } else if (parentId == 0) {
            Long usercompanyId = cscpOrgService.getById(SecurityUtils.getCurrentCscpUserDetail().getCompanyId()).getParentId();
            List<Node<CscpOrgDTO>> orgName = cscpOrgService.selectChildrenListNodeByParentId(usercompanyId, ids);
            return orgName;
        } else {
            List<Node<CscpOrgDTO>> orgName = cscpOrgService.selectChildrenListNodeByParentId(parentId, ids);
            return orgName;
        }
    }

    /**
     * 根据id查询所有下级机构,id为0时查询所有顶层单位
     *
     * @param id
     * @return
     */
    @Override
    public List<CscpOrgDTO> selectOrgById(Long id) {
        return selectOrgById(id,null,null,null);
    }
    // all 查所有
    @Override
    public List<CscpOrgDTO> selectOrgById(Long id, String queryType,String orgCodePath, String orgName) {
        log.info("CscpOrgServicelmpl.selectOrgById Long = {}", id);
        if (Objects.isNull(id)) {
            throw new BusinessException("机构id不能为空");
        } else {
            LambdaQueryWrapper<CscpOrg> cscpOrgLambdaQueryWrapper = Wrappers.lambdaQuery();
            List<Long> ids = new ArrayList<>();
            if(!StrUtil.equals("all",queryType)){
                //普通用户输入id为0时查看本单位部门,普通用户不显示虚拟机构。单位管理员显示虚拟机构
                if (SecurityUtils.isNormalName() && CollUtil.isEmpty(SecurityUtils.getCurrentCscpUserDetail().getModeratorAppIdList())) {
                    cscpOrgLambdaQueryWrapper.ne(CscpOrg::getType, OrgTypeEnum.ORG_TYPE_1.getCode());
                    if (id == 0) {
                        id = SecurityUtils.getCurrentCscpUserDetail().getCompanyId();
                    }
                }
            }

            Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
            if (currentCompanyId != null && CollUtil.isEmpty(SecurityUtils.getCurrentCscpUserDetail().getModeratorAppIdList())) {
                CscpOrg byId = this.getById(currentCompanyId);
                if(id.equals(0L)){
                    if (SecurityUtils.isDepartAdmin() && !SecurityUtils.isUnitAdmin() && !SecurityUtils.isRegionAdmin()) {
                        List<TDeptManagementAuthorityDTO> tDeptManagementAuthorityDTOList = tDeptManagementAuthorityService.findManageDeptByUserId(SecurityUtils.getCurrentUserId());
                        if (CollectionUtils.isNotEmpty(tDeptManagementAuthorityDTOList)) {
                            ids.addAll(tDeptManagementAuthorityDTOList.stream().map(TDeptManagementAuthorityDTO::getDeptId).collect(Collectors.toList()));
                        }
                    } else {
                        cscpOrgLambdaQueryWrapper.eq(CscpOrg::getId, byId.getId());
                    }
                }else {
                    cscpOrgLambdaQueryWrapper.eq(CscpOrg::getParentId, id);
                }
                // 已经通过id或者parentId了，不需要通过orgCode,而且orgCode有特殊情况
                // cscpOrgLambdaQueryWrapper.like(CscpOrg::getOrgCode,byId.getOrgCode());
            }else{
                // 不能跨顶层，市区县下不能跨市
                if (StringUtils.isNotEmpty(orgCodePath)) {
                    String[] split = orgCodePath.trim().split("\\|");
                    if (id == 0) {
                        if (split.length == 1) {
                            return Collections.emptyList();
                        } else {
                            cscpOrgLambdaQueryWrapper.eq(CscpOrg::getOrgCode, split[0]);
                        }
                    } else {
                        if (split.length > 2 && split[1].length() == 12) {
                            CscpOrg cscpOrg = cscpOrgRepository.selectById(id);
                            if (!cscpOrg.getOrgCodePath().contains("|")) {
                                cscpOrgLambdaQueryWrapper.eq(CscpOrg::getOrgCode, split[1]);
                            }
                        }
                        cscpOrgLambdaQueryWrapper.eq(CscpOrg::getParentId, id);
                    }
                } else {
                    cscpOrgLambdaQueryWrapper.eq(CscpOrg::getParentId, id);
                }
            }
            cscpOrgLambdaQueryWrapper.in(!CollUtil.isEmpty(ids), CscpOrg::getId, ids);
            cscpOrgLambdaQueryWrapper.like(StringUtils.isNotEmpty(orgName), CscpOrg::getOrgName, orgName);
            cscpOrgLambdaQueryWrapper.orderByAsc(CscpOrg::getOrderBy);
            List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(cscpOrgLambdaQueryWrapper);
            List<CscpOrgDTO> cscpOrgDTOList = ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
            //判断是否有下级单位并设置标识
            log.info("CscpOrgServiceImpl, currentUserId = {}, cscpOrgDTOListr = {}", SecurityUtils.getCurrentUserId(), cscpOrgDTOList.size());
            cscpOrgDTOList.forEach(i -> {
                if (this.hasChildOrg(i.getId(), null)) {
                    i.setIsValuable(1);
                } else {
                    i.setIsValuable(0);
                }
                if(StringUtils.isNotEmpty(i.getModelDataType())){
                    String[] split = i.getModelDataType().trim().split(",");
                    List<String> asList = Arrays.asList(split);
                    i.setModelDataTypeList(asList);
                }

                if(StringUtils.isNotEmpty(i.getModelName())){
                    String[] split = i.getModelName().trim().split(",");
                    List<String> asList = Arrays.asList(split);
                    i.setModelNameList(asList);
                }

                if (westoneEncryptService.isCipherMachine()) {
                    // TODO 数据完整性校验
                    String orgCode = i.getOrgCode();
                    if (StringUtils.isNotNull(orgCode)) {
                        String hmacOrgCode = i.getHmacOrgCode();
                        boolean isIntegrity = westoneEncryptService.compareSM3HMAC(i.getOrgName() + i.getOrgCode(), hmacOrgCode);
                        if (isIntegrity) {
                            i.setDataIsIntegrity(0);
                        } else {
                            i.setDataIsIntegrity(1);
                        }
                    } else {
                        i.setDataIsIntegrity(0);
                    }
                }
            });
            return cscpOrgDTOList;
        }
    }

    @Override
    public List<CscpOrgDTO> selectOrgCityState() {
        // 市州县市区 编码
        Long id = 1666366505094488066L;
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectOrgCityStateList(id);
        return ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
    }


    @Override
    public List<CscpOrgDTO> getOrgWithAllParents(String orgName) {
        // 1. 查询匹配机构
        List<CscpOrg> matchedOrgs = cscpOrgRepository.selectList(
                new LambdaQueryWrapper<CscpOrg>()
                        .like(StringUtils.isNotEmpty(orgName), CscpOrg::getOrgName, orgName)
        );

        // 2. 收集所有相关ID（匹配机构及其父链）
        Set<Long> allIds = new LinkedHashSet<>();
        matchedOrgs.forEach(org -> {
            collectAllParentIds(org.getId(), allIds);
        });

        // 3. 批量查询所有相关机构
        List<CscpOrg> allOrgs = cscpOrgRepository.selectList(
                new LambdaQueryWrapper<CscpOrg>().in(!allIds.isEmpty(), CscpOrg::getId, allIds)
        );

        // 4. 转换为DTO并保持插入顺序
        return allOrgs.stream()
                .map(org -> {
                    CscpOrgDTO dto = new CscpOrgDTO();
                    BeanUtils.copyProperties(org, dto);
                    return dto;
                })
                .collect(Collectors.toList());
    }

    private void collectAllParentIds(Long currentId, Set<Long> idSet) {
        while (currentId != null && currentId != 0L) {
            if (!idSet.add(currentId)) break; // 防止循环引用

            CscpOrg org = cscpOrgRepository.selectById(currentId);
            currentId = (org != null) ? org.getParentId() : null;
        }
    }





    /**
     * 只能使用在本租户或者本单位中查询所有机构信息,不需要勾选已经选择的节点，带用户名搜索
     *
     * @param parentId
     * @param realName
     * @return
     */
    //TODO 将刘哥写在controller的代码迁移到service中
    @Override
    public CscpOrgAndUserDto selectOrgAndQueryRealNameList(Long parentId, String realName, Integer onlyDepart) {
        CscpOrgAndUserDto cscpOrgAndUserDto = new CscpOrgAndUserDto();
        List<CscpUserDTO> cscpUserDTOList = new ArrayList<>();
        // 查找用户姓名
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(realName)) {
            CscpUserDTO cscpUserDTO = new CscpUserDTO();
            cscpUserDTO.setRealName(realName);
            cscpUserDTO.setStatus(1);
            PageResult<CscpUserDTO> byCscpUserDTO = cscpUserService.findByCscpUserDTO(cscpUserDTO, new BasePageForm(1, BasePageForm.MAx_PAGE_SIZE));
            cscpUserDTOList = byCscpUserDTO.getData();
        }

        Node<CscpOrgDTO> parentNode = new Node<>();
        // 普通用户只看到自己单位的
        if (SecurityUtils.isGeneralName()) {
            parentId = SecurityUtils.getCurrentCscpUserDetail().getCompanyId();
            CscpOrgDTO cscpOrgDTO = cscpOrgService.findOne(parentId);
            parentNode.setId(parentId);
            parentNode.setTitle(cscpOrgDTO.getOrgName());
            parentNode.setDetailsData(cscpOrgDTO);
        }
        List<Node<CscpOrgDTO>> nodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(cscpUserDTOList)) {
            //  如果同一单位下面的，直接把部门取出，用第一个即可
            Set<Long> departIds = new HashSet<>();
            cscpUserDTOList.forEach(cscpUserDTO -> {
                if (cscpUserDTO.getDepartmentId() != null) {
                    departIds.add(cscpUserDTO.getDepartmentId());
                }
            });
            ArrayList<Long> longs = CollectionUtil.newArrayList(departIds);
            nodeList = cscpOrgService.selectCheckedOrgNodeTree(parentId, longs);
            cscpOrgAndUserDto.setCscpUserDTOList(cscpUserDTOList);
        } else if (ObjectUtil.equal(onlyDepart, 1)){
            // 流程转公告和传阅，仅发本部门
            Node<CscpOrgDTO> node = this.queryOwnerCompanyNode();
            nodeList.add(node);
        }else{
            // 查询是否配置“通知公告全单位角色”
            LambdaQueryWrapper<CscpRoles> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpRoles::getName,"通知公告全单位角色").last(SysConstant.LIMIT_ONE );
            CscpRoles cscpRoles = cscpRolesRepository.selectOneNoAdd(queryWrapper);
            if (Objects.nonNull(cscpRoles)){
                Boolean hasRole = cscpUserRoleService.verifyUserRole(cscpRoles.getId(), SecurityUtils.getCurrentUserId());
                // 当前用户是否在“通知公告全单位角色”中
                if (hasRole){
                    // 是，表示可以发全部门
                    nodeList = cscpOrgService.selectChildrenListNodeByParentId(parentId);
                }else {
                    //否，只能发本部门
                    Node<CscpOrgDTO> node = this.queryOwnerCompanyNode();
                    nodeList.add(node);
                }
            }else {
                // 没有配置角色，默认发全单位
                nodeList = cscpOrgService.selectChildrenListNodeByParentId(parentId);
            }
        }

        if (SecurityUtils.isGeneralName()) {
            parentNode.setChildren(nodeList);
            List<Node<CscpOrgDTO>> list = new ArrayList<Node<CscpOrgDTO>>();
            list.add(parentNode);
            cscpOrgAndUserDto.setCscpOrgNode(list);
        } else {
            cscpOrgAndUserDto.setCscpOrgNode(nodeList);
        }
        return cscpOrgAndUserDto;
    }

    /**
     * 查询实际考核人数
     *
     * @return
     */
    @Override
    public List<CscpOrgDTO> selectAssessmentPeopleCount() {
        List<CscpOrg> orgList = Optional.ofNullable(cscpOrgRepository.selectListNoAdd(new LambdaQueryWrapper<CscpOrg>()
                .select(CscpOrg::getId, CscpOrg::getOrgName, CscpOrg::getAssessmentPeopleCount).eq(CscpOrg::getCheckUp, 1))).orElse(new ArrayList<>());
        return ListCopyUtil.copy(orgList, CscpOrgDTO.class);
    }

    /**
     * 修改实际考核人数
     *
     * @param cscpAssessmentPeopleCountDTOS
     * @return
     */
    @Override
    public Boolean updateAssessmentPeopleCount(List<CscpAssessmentPeopleCountDTO> cscpAssessmentPeopleCountDTOS) {
        if (Objects.isNull(cscpAssessmentPeopleCountDTOS) && cscpAssessmentPeopleCountDTOS.isEmpty()) {
            throw new BusinessException("集合不能为空!");
        }

        List<CscpOrg> orgs = cscpAssessmentPeopleCountDTOS.stream().map(i -> {
            CscpOrg cscpOrg = new CscpOrg();
            cscpOrg.setId(i.getId());
            cscpOrg.setAssessmentPeopleCount(i.getAssessmentPeopleCount());
            return cscpOrg;
        }).collect(Collectors.toList());

        return this.updateBatchById(orgs);
    }


    //分割字符串加密
    public String division(String realName) {
        String realNames = "";
        if (org.apache.commons.lang3.StringUtils.isNotBlank(realName)) {
            for (int i = 0; i < realName.length(); i++) {
                realNames = realNames.concat(KeyCenterUtils.encrypt(realName.substring(i, i + 1)));
                realNames = realNames.concat(",");
            }
            realNames = realNames.substring(0, realNames.length() - 1);
        }
        return realNames;
    }


    @Override
    @Transactional
    public CscpOrgDTO crmSave(CscpOrgDTO cscpOrgDTO) {
        log.info("领航开通客户群组开始");
        log.info("获取到领航信息新增单位= {}", cscpOrgDTO);
        if (Objects.isNull(cscpOrgDTO.getParentId())) {
            throw new BusinessException("上级节点不能为空");
        }
        // 是否考核设置默认值
        if (Objects.isNull(cscpOrgDTO.getCheckUp())) {
            cscpOrgDTO.setCheckUp(false);
        }
        String crmtype = cscpOrgDTO.getCrmTenantType();
        LambdaQueryWrapper<TSysTenant> queryWrapperUser = new LambdaQueryWrapper<>();
        if("3".equals(crmtype)){
            crmtype = "2";
            queryWrapperUser.eq(TSysTenant::getCrmTenantType, crmtype);
        }else{
            queryWrapperUser.eq(TSysTenant::getCrmTenantType, cscpOrgDTO.getCrmTenantType());
        }
        TSysTenant tSysTenant = tSysTenantMapper.selectOneNoAdd(queryWrapperUser);

        CscpOrgDTO dto = context.crmExecuteStrategy(cscpOrgDTO,tSysTenant);
        BeanUtils.copyProperties(dto, cscpOrgDTO);
        return cscpOrgDTO;
    }

    @Override
    @Transactional
    public int crmUpdate(CscpOrgDTO cscpOrgDTO) {
        log.info("获取到领航信息更新单位 = {}", cscpOrgDTO);
        // 把大于该排序号的所有排序号都 + 1
        context.crmexecuteUpdateSortStrategy(cscpOrgDTO);

        // 插入组织机构
        CscpOrg cscpOrg = new CscpOrg();
        BeanUtils.copyProperties(cscpOrgDTO, cscpOrg);
        int t = cscpOrgRepository.updateById(cscpOrg);

        return t;
    }

    @Override
    public List<Long> getAllChildOrgIds(Long orgId , List<Long> labelIds) {
        Set<Long> set = new HashSet<>();
        this.getAllChildOrgIds(SetUtils.hashSet(orgId) ,set ,labelIds);
        return set.stream().collect(Collectors.toList());
    }

    /**
     * 根据租户id 查询所有单位 平级展示所有单位信息
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<CscpOrgDTO> selectAllCompany(Long tenantId) {
        List<CscpOrgDTO> companyLst = new ArrayList<>();
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        if (Objects.nonNull(tenantId)) {
            queryWrapper.eq(CscpOrg::getTenantId, tenantId);
        }
        queryWrapper.eq(CscpOrg::getType, 2);
        queryWrapper.eq(CscpOrg::getDeleted, 0);
        queryWrapper.orderByAsc(CscpOrg::getOrderBy);
        List<CscpOrg> orgLst = cscpOrgRepository.selectListNoAdd(queryWrapper);
        companyLst = ListCopyUtil.copy(orgLst, CscpOrgDTO.class);
        return companyLst;
    }

    @Override
    public List<CscpOrgDTO> selectAllCompanyByOrgName(String orgName) {
        List<CscpOrgDTO> companyLst = new ArrayList<>();
        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotEmpty(orgName)) {
            queryWrapper.like(CscpOrg::getOrgName, orgName);
        }
        queryWrapper.eq(CscpOrg::getType, 2);
        queryWrapper.eq(CscpOrg::getDeleted, 0);
        queryWrapper.orderByAsc(CscpOrg::getOrderBy);
        List<CscpOrg> orgLst = cscpOrgRepository.selectListNoAdd(queryWrapper);
        companyLst = ListCopyUtil.copy(orgLst, CscpOrgDTO.class);
        return companyLst;
    }

    /**
     * 此接口用来解决，办公厅下面机要局，机要局下面信息中心，但是要取值信息中心
     * 先找人说在的部门，如果上面还有部门，取上面部门信息，如果上面时单位，取单位信息
     * @return
     */
    @Override
    public CscpOrgDTO getCurrentParentOrg() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        Long companyId = cscpUserDetail.getCompanyId();
        Long deparentId = cscpUserDetail.getDepartmentId();
        CscpOrg cscpOrg = cscpOrgService.getById(deparentId);
        if (cscpOrg.getType() == 3){
            CscpOrg cscpOrgParent  = cscpOrgService.getById(cscpOrg.getParentId());
            if (cscpOrgParent != null ){
                if (cscpOrgParent.getType() == 2){
                    CscpOrgDTO cscpOrgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
                    return cscpOrgDTO;
                }else if (cscpOrgParent.getType() == 3){
                    CscpOrgDTO cscpOrgDTO = BeanConvertUtils.copyProperties(cscpOrgParent, CscpOrgDTO.class);
                    return cscpOrgDTO;
                }
            }

        }else{
            CscpOrgDTO cscpOrgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
            return cscpOrgDTO;
        }
        CscpOrgDTO cscpOrgDTO = BeanConvertUtils.copyProperties(cscpOrg, CscpOrgDTO.class);
        return cscpOrgDTO;
    }

    @Override
    public CscpOrg getTopCompanyIdByUserId(Long userId) {
        List<CscpUserOrg> cscpUserOrgList = cscpUserOrgRepository.selectListNoAdd(Wrappers.<CscpUserOrg>lambdaQuery()
                .eq(CscpUserOrg::getUserId, userId));
        if (CollectionUtils.isEmpty(cscpUserOrgList)) {
            return null;
        }
        CscpUserOrg cscpUserOrg = cscpUserOrgList.get(0);
        for (CscpUserOrg userOrg : cscpUserOrgList) {
            if (ObjectUtil.equal(userOrg.getOrgId(), SecurityUtils.getCurrentCscpUserDetail().getDepartmentId())) {
                cscpUserOrg = userOrg;
                break;
            }
        }
        return getTopCompanyId(cscpUserOrg.getOrgId());
    }

    private CscpOrg getTopCompanyId(Long orgId) {
        CscpOrg cscpOrg = cscpOrgService.getById(orgId);
        if (cscpOrg.getParentId() == null || cscpOrg.getParentId() == 0L || cscpOrg.getType() == 2) {
            return cscpOrg;
        } else {
            return getTopCompanyId(cscpOrg.getParentId());
        }
    }

    /**
     * 获得所有 机构ids
     * @param porgIds 父id
     * @param allOrgIds 所有id
     */
    private void getAllChildOrgIds(Set<Long> porgIds , Set<Long> allOrgIds, List<Long> labelIds) {
        allOrgIds.addAll(porgIds);
        for (Long pOrgId : porgIds) {
            Set<Long> childs = this.getChildOrgListNoAdd(pOrgId , labelIds).stream().map(i -> i.getId()).collect(Collectors.toSet());
            if(!childs.isEmpty()){
                getAllChildOrgIds(childs,allOrgIds, labelIds);
            }
        }
    }
    public List<TLabelOrgDTO> getChildOrgListNoAdd(Long orgId, List<Long> labelIds) {
        // LambdaQueryWrapper<TLabelOrg> queryWrapper = new LambdaQueryWrapper();
        // queryWrapper.eq(TLabelOrg::getOrgId, orgId).in(!labelIds.isEmpty(),TLabelOrg::getDisplayRangeId,labelIds);
        // List<TLabelOrg> childOrgList = tLabelOrgMapperl.selectListNoAdd(queryWrapper);
        List<TLabelOrgDTO> orgTxl =  cscpOrgRepository.getChildOrgListNoAddTxl(orgId,labelIds);

        return orgTxl;
    }

    /**
     * 检查登录用户是否有查询该用户的权限
     * 1.admin可以查看所有用户
     * 2.租户管理员可以查看本租户下所有用户
     * 3.单位管理员可以查看本单位下所有用户
     * 4.普通用户仅能查看本人
     */
    public boolean checkUserPermissions(Long userId){
        CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (currentCscpUserDetail.getId() == 1 && currentCscpUserDetail.getId() == userId) {
            return true;
        }
        LambdaQueryWrapper<CscpUserRole> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpUserRole::getUserId,currentCscpUserDetail.getId());
        queryWrapper.eq(CscpUserRole::getDeleted,0);
        List<CscpUserRole> cscpUserRoles = cscpUserRoleService.selectListNoAdd(queryWrapper);

        List<Long> roleIDs=new ArrayList<>();
        for (CscpUserRole cscpUserRole : cscpUserRoles) {
            roleIDs.add(cscpUserRole.getRoleId());
        }

        List<CscpRoles> cscpRoles = cscpRolesRepository.selectBatchIds(roleIDs);
        for (CscpRoles cscpRole : cscpRoles) {
            if(cscpRole.getName().contains("租户管理员")){
                return true;
            }
            if(cscpRole.getName().contains("单位管理员")){
                return true;
            }
        }
        return false;
    }

    /**
     * 只查询本单位
     * */
    public Node<CscpOrgDTO> queryOwnerCompanyNode(){
        LambdaQueryWrapper<CscpOrg> qworg = new LambdaQueryWrapper();
        qworg.eq(CscpOrg::getId,SecurityUtils.getCurrentCscpUserDetail().getDepartmentId());
        CscpOrg cscpOrg = cscpOrgRepository.selectOneNoAdd(qworg);
        CscpOrgDTO dto = new CscpOrgDTO();
        BeanUtils.copyProperties(cscpOrg,dto);
        Node<CscpOrgDTO> node = new Node<>();
        node.setDetailsData(dto);
        node.setId(dto.getId());
        node.setTitle(dto.getTitle());
        return node;
    }


    @Override
    public CscpOrgAndUserDto selectTenantTreeOrgAndUser(CscpTenantOrgTreeQueryDTO req) {
        CscpUserDetail userDetail = SecurityUtils.getCurrentCscpUserDetail();
//        boolean flag = cscpUserRoleService.isLocalTenantManager();
//        if(flag){
        //查询当前租户下所有单位信息
        req.setTenantId(userDetail.getTenantId());
//        }else if (SecurityUtils.isGeneralName()){
//            //普通用户 默认带上单位id查询数据
//            req.setCompanyId(userDetail.getCompanyId());
//        }
        CscpOrgAndUserDto ret = new CscpOrgAndUserDto();
        //查询机构
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpOrg::getDeleted, 0);
        if (req.getCompanyId() != null) {
            queryWrapper.eq(CscpOrg::getCompanyId, req.getCompanyId());
        } else if (req.getTenantId() != null) {
            queryWrapper.eq(CscpOrg::getTenantId, req.getTenantId());
        }
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        List<CscpOrgDTO> orgDTOList = getDataDtOFromDomin(orgList);
        //构建tree
        List<Node<CscpOrgDTO>> cscpOrgNode = buildTree(orgDTOList);
        ret.setCscpOrgNode(cscpOrgNode);
        ret.setCscpUserDTOList(Lists.newArrayList());
        return ret;
    }

    @Override
    public void addService(CscpOrg vo) {
        List<CscpOrg> list=selectOrgByParentId(vo.getId());
        cscpOrgRepository.updateById(vo);
        //修改该单位下所有用户的关联数据源信息
        if(list==null||list.size()<=0){
            return;
        }
        for(CscpOrg org:list){
            org.setServiceId(vo.getServiceId());
            addService(org);
        }
    }


    @Override
    public List<CscpOrg> selectOrgByParentId(Long id) {
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpOrg::getParentId, id);
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        return orgList;
    }


    @Override
    public List<CscpOrgDTO> selectDivisionOrgById(Long id, String orgCodePath) {
        CscpUserDetail userDetail = SecurityUtils.getCurrentCscpUserDetail();
        CscpOrg cscpOrg = cscpOrgRepository.selectById(userDetail.getCompanyId());
        if(cscpOrg != null){
            LambdaQueryWrapper<CscpOrg> cscpOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            String orgCode = cscpOrg.getOrgCode();
            if(id == null){
                if("4300".equals(orgCode.substring(0,4))){
                    //查省直机构顶级数据
                    cscpOrgLambdaQueryWrapper.eq(CscpOrg::getParentId, 0);
                    cscpOrgLambdaQueryWrapper.like(CscpOrg::getOrgCode, orgCode.substring(0,4));
                } else {
                    //查市直、县市区、乡镇机构顶级数据
                    cscpOrgLambdaQueryWrapper.eq(CscpOrg::getOrgCode,orgCode.substring(0,12));
                }
            } else {
                cscpOrgLambdaQueryWrapper.eq(CscpOrg::getParentId,id);
                if("4300".equals(orgCode.substring(0,4))){
                    cscpOrgLambdaQueryWrapper.like(CscpOrg::getOrgCode, orgCode.substring(0,4));
                } else if ("00".equals(orgCode.substring(0,6).substring(4,6))){
                    //查市直机构的下级机构数据
                    cscpOrgLambdaQueryWrapper.like(CscpOrg::getOrgCode,orgCode.substring(0,4));
                } else if ("000".equals(orgCode.substring(0,9).substring(6,9))){
                    //查县市区机构的下级机构数据
                    cscpOrgLambdaQueryWrapper.like(CscpOrg::getOrgCode,orgCode.substring(0,6));
                } else {
                    //查询乡镇机构的下级机构数据
                    cscpOrgLambdaQueryWrapper.like(CscpOrg::getOrgCode,orgCode.substring(0,9));
                }
            }
            // 不能跨顶层，市区县下不能跨市
            if (StringUtils.isNotEmpty(orgCodePath)) {
                String[] split = orgCodePath.trim().split("\\|");
                if ((id == null || id == 0)) {
                    if ("4300".equals(orgCode.substring(0,4))) {
                        if (split.length == 1) {
                            return Collections.emptyList();
                        } else {
                            cscpOrgLambdaQueryWrapper.eq(CscpOrg::getOrgCode, split[0]);
                        }
                    }
                }
            }

            cscpOrgLambdaQueryWrapper.orderByAsc(CscpOrg::getOrderBy);
            List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(cscpOrgLambdaQueryWrapper);
            List<CscpOrgDTO> cscpOrgDTOList = ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
            //判断是否有下级单位并设置标识
            cscpOrgDTOList.forEach(i -> {
                if (this.hasDivisionChildOrg(i.getId(), orgCode)) {
                    i.setIsValuable(1);
                } else {
                    i.setIsValuable(0);
                }
                if(StringUtils.isNotEmpty(i.getModelDataType())){
                    String[] split = i.getModelDataType().trim().split(",");
                    List<String> asList = Arrays.asList(split);
                    i.setModelDataTypeList(asList);
                }

                if(StringUtils.isNotEmpty(i.getModelName())){
                    String[] split = i.getModelName().trim().split(",");
                    List<String> asList = Arrays.asList(split);
                    i.setModelNameList(asList);
                }
            });
            return cscpOrgDTOList;
        }
        return null;
    }

    @Override
    public List<CscpOrgDTO> selectDivisionOrgById_new(Long id, String orgCodePath) {
        List<CscpOrgDTO> cscpOrgDTOList;
        List<CscpOrg> cscpOrgList = new ArrayList<>();
        if (id == null || id == 0) {
            CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            if (cscpUserDetail.getCompanyId() != null) {
                CscpOrg cscpOrg = cscpOrgRepository.selectById(cscpUserDetail.getCompanyId());
                if (cscpOrg != null) {
                    cscpOrgList.add(cscpOrg);
                }
            }
            if (CollectionUtils.isEmpty(cscpOrgList)) {
                if (cscpUserDetail.getDepartmentId() != null) {
                    CscpOrg cscpOrg = cscpOrgRepository.selectById(cscpUserDetail.getDepartmentId());
                    if (cscpOrg != null) {
                        cscpOrgList.add(cscpOrg);
                    }
                }
            }
        } else {
            LambdaQueryWrapper<CscpOrg> cscpOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
            cscpOrgLambdaQueryWrapper.eq(CscpOrg::getParentId,id);
            cscpOrgLambdaQueryWrapper.orderByAsc(CscpOrg::getOrderBy);
            cscpOrgList = cscpOrgRepository.selectListNoAdd(cscpOrgLambdaQueryWrapper);
        }
        if (CollectionUtils.isNotEmpty(cscpOrgList)) {
            cscpOrgDTOList = ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
            //判断是否有下级并设置标识
            cscpOrgDTOList.forEach(i -> {
                LambdaQueryWrapper<CscpOrg> cscpOrgLambdaQueryWrapper2 = new LambdaQueryWrapper<>();
                cscpOrgLambdaQueryWrapper2.eq(CscpOrg::getParentId,i.getId());
                List<CscpOrg> cscpOrgList2 = cscpOrgRepository.selectListNoAdd(cscpOrgLambdaQueryWrapper2);
                if (CollectionUtils.isNotEmpty(cscpOrgList2)) {
                    i.setIsValuable(1);
                } else {
                    i.setIsValuable(0);
                }
                if(StringUtils.isNotEmpty(i.getModelDataType())){
                    String[] split = i.getModelDataType().trim().split(",");
                    List<String> asList = Arrays.asList(split);
                    i.setModelDataTypeList(asList);
                }

                if(StringUtils.isNotEmpty(i.getModelName())){
                    String[] split = i.getModelName().trim().split(",");
                    List<String> asList = Arrays.asList(split);
                    i.setModelNameList(asList);
                }
            });
            return cscpOrgDTOList;
        }
        return Collections.emptyList();
    }

    /**
     * 构建机构树
     * @param orgDTOList
     * @return
     */
    private List<Node<CscpOrgDTO>> buildTree(List<CscpOrgDTO> orgDTOList){
        List<Node<CscpOrgDTO>> ret = Lists.newArrayList();
        Map<Long, Node<CscpOrgDTO>> nodeMap = Maps.newHashMap();
        for (CscpOrgDTO orgDTO : orgDTOList){
            Node<CscpOrgDTO> node = new Node<>();
            node.setId(orgDTO.getId());
            node.setTitle(orgDTO.getOrgName());
            node.setDetailsData(orgDTO);
            nodeMap.put(node.getId(), node);
        }
        for (CscpOrgDTO cscpOrgDTO : orgDTOList){
            Long parentId = cscpOrgDTO.getParentId();
            Node<CscpOrgDTO> parentNode = nodeMap.get(parentId);
            if (parentNode == null){
                continue;
            }
            List<Node<CscpOrgDTO>> children = parentNode.getChildren();
            if (children == null){
                children = Lists.newArrayList();
                parentNode.setChildren(children);
            }
            children.add(nodeMap.get(cscpOrgDTO.getId()));
        }
        // 找到顶级节点 放到返回对象里面
        for (Map.Entry<Long, Node<CscpOrgDTO>> entry : nodeMap.entrySet()){
            Node<CscpOrgDTO> node = entry.getValue();
            CscpOrgDTO cscpOrgDTO = node.getDetailsData();
            if (cscpOrgDTO.getParentId() == null || cscpOrgDTO.getParentId().equals(0L)){
                ret.add(node);
            }
        }
        return ret;
    }

    private Boolean hasDivisionChildOrg(Long orgId, String orgCode) {
        log.info("CscpOrgServiceImpl.hasDivisionChildOrg orgId = {} orgCode={}", orgId, orgCode);
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpOrg::getParentId, orgId);
        if("4300".equals(orgCode.substring(0,4))){
            queryWrapper.like(CscpOrg::getOrgCode, orgCode.substring(0,4));
        } else if ("00".equals(orgCode.substring(0,6).substring(4,6))){
            //查市直机构的下级机构数据
            queryWrapper.like(CscpOrg::getOrgCode,orgCode.substring(0,4));
        } else if ("000".equals(orgCode.substring(0,9).substring(6,9))){
            //查县市区机构的下级机构数据
            queryWrapper.like(CscpOrg::getOrgCode,orgCode.substring(0,6));
        } else {
            //查询乡镇机构的下级机构数据
            queryWrapper.like(CscpOrg::getOrgCode,orgCode.substring(0,9));
        }
        Integer count = cscpOrgRepository.selectCountNoAdd(queryWrapper);
        return count > 0;
    }


    /**
     * 处理所有level为null的数据
     *
     * @return 更新的记录数
     */
    @Override
    @Transactional
    public Integer processNullLevels() {
        log.debug("处理cscp_org中所有level为空null的数据");

        // 1. 查询所有数据
        List<CscpOrg> allOrgList = cscpOrgService.list();
        if (allOrgList == null || allOrgList.isEmpty()) {
            return 0;
        }

        // 2. 按parentId分组
        Map<Long, List<CscpOrg>> groupedByParent = allOrgList.stream()
                .collect(Collectors.groupingBy(CscpOrg::getParentId));

        int totalUpdated = 0;

        // 3. 对每个parentId分组进行处理
        for (Map.Entry<Long, List<CscpOrg>> entry : groupedByParent.entrySet()) {
            Long parentId = entry.getKey();
            List<CscpOrg> orgList = entry.getValue();

            // 找到第一个level不为null的值
            Integer firstNonNullLevel = null;
            for (CscpOrg org : orgList) {
                if (org.getLevel() != null) {
                    firstNonNullLevel = org.getLevel();
                    log.debug("找到父ID为 {} 的第一个非空级别: {}", parentId, firstNonNullLevel);
                    break;
                }
            }

            // 如果没有找到不为null的level，则跳过当前分组
            if (firstNonNullLevel == null) {
                log.warn("父ID为 {} 的记录中未找到非空级别", parentId);
                continue;
            }

            // 统计当前分组中需要更新的记录数
            List<CscpOrg> nullLevelOrgs = orgList.stream()
                    .filter(org -> org.getLevel() == null)
                    .collect(Collectors.toList());

            int count = nullLevelOrgs.size();
            if (count == 0) {
                log.info("父ID为 {} 的记录无需更新", parentId);
                continue;
            }

            // 更新当前分组中所有level为null的记录
            LambdaUpdateWrapper<CscpOrg> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(CscpOrg::getParentId, parentId)
                    .isNull(CscpOrg::getLevel)
                    .set(CscpOrg::getLevel, firstNonNullLevel);

            boolean updated = cscpOrgService.update(updateWrapper);
            if (updated) {
                log.info("成功更新父ID为 {} 的 {} 条记录", parentId, count);
                totalUpdated += count;
            } else {
                log.warn("更新父ID为 {} 的记录失败", parentId);
            }
        }

        log.info("总共更新的记录数: {}", totalUpdated);
        return totalUpdated;
    }

    @Override
    public void initOrgPath() {
        // 先设置根节点的路径信息
        cscpOrgRepository.updateRootPath();

        // 所有机构
        List<CscpOrg> allOrgs = cscpOrgRepository.selectListNoAdd(
                new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getDeleted, 0));

        // 根节点
        Queue<CscpOrg> queue = allOrgs.stream()
                .filter(org -> org.getParentId() == null || org.getParentId() == 0).collect(Collectors.toCollection(LinkedList::new));

        // BFS
        while (!queue.isEmpty()) {
            CscpOrg current = queue.poll();

            // 查找当前节点的所有子节点
            List<CscpOrg> children = allOrgs.stream()
                    .filter(org -> org.getParentId() != null && org.getParentId().equals(current.getId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(children)) {
                for (CscpOrg child : children) {
                    try {
                        // 更新子节点路径信息
                        child.setOrgIdPath(StringUtils.isEmpty(current.getOrgIdPath()) ? current.getId().toString()
                                : current.getOrgIdPath() + "|" + current.getId());
                        child.setOrgCodePath(current.getOrgCodePath() + "|" + child.getOrgCode());
                        child.setOrderByPath(current.getOrderByPath() + (100000 + child.getOrderBy()));
                        child.setLevel(current.getLevel() + 1);

                        // 更新数据库
                        cscpOrgService.updateById(child);

                        // 加入队列继续处理
                        queue.add(child);
                    } catch (Exception e) {
                        log.error("更新子级机构层级数据失败，转人工处理:" + e + ";chilid：" + JSON.toJSONString(child) + ";parent:" + JSON.toJSONString(current));
                    }
                }
            }
        }
    }

    @Override
    public List<CscpOrgDTO> getOrgWithAllParentsNew(String orgName) {

        long t1 = System.currentTimeMillis();

        // 1. 查询匹配机构
        List<CscpOrg> matchedOrgs = cscpOrgRepository.selectList(
                new LambdaQueryWrapper<CscpOrg>()
                        .like(StringUtils.isNotEmpty(orgName), CscpOrg::getOrgName, orgName)
        );

        log.info("查询匹配机构time:" + ( System.currentTimeMillis() - t1));

        // 2.收集匹配机构及其上级机构Code
        Set<String> allCodes = new HashSet<>();
        matchedOrgs.forEach(org -> {
            if (StringUtils.isEmpty(org.getOrgCodePath())) {
                return;
            }
            allCodes.addAll(Arrays.asList(org.getOrgCodePath().split("\\|")));
        });

        // 3. 批量查询所有相关机构
        List<CscpOrg> allOrgs = cscpOrgRepository.selectList(
                new LambdaQueryWrapper<CscpOrg>().in(!allCodes.isEmpty(), CscpOrg::getOrgCode, allCodes)
        );

        // 4. 转换为DTO并保持插入顺序
        List<CscpOrgDTO> orgDTOs = allOrgs.stream()
                .map(org -> {
                    CscpOrgDTO dto = new CscpOrgDTO();
                    BeanUtils.copyProperties(org, dto);
                    return dto;
                })
                .collect(Collectors.toList());

        // 5. 构建树结构
        Map<Long, CscpOrgDTO> nodeMap = new HashMap<>();
        for (CscpOrgDTO orgDTO : orgDTOs) {
            nodeMap.put(orgDTO.getId(), orgDTO);
        }

        for (CscpOrgDTO orgDTO : orgDTOs) {
            Long parentId = orgDTO.getParentId();
            if (parentId != null && nodeMap.containsKey(parentId)) {
                CscpOrgDTO parentNode = nodeMap.get(parentId);
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList<>());
                }
                parentNode.getChildren().add(orgDTO);
            }
        }

        // 6. 返回顶级节点
        return orgDTOs.stream()
                .filter(orgDTO -> orgDTO.getParentId() == 0 || !nodeMap.containsKey(orgDTO.getParentId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<CscpOrgDTO> getOrgWithAllParentsNewest(String orgName, Long orgId, Integer allFlag) {
        boolean isAdmin = SecurityUtils.isAdmin();
        if (allFlag !=null && allFlag == 1) {
            isAdmin = true;
        }
        //如果当前用户是版主，则改成admin搜索
        if(CollUtil.isNotEmpty(SecurityUtils.getCurrentCscpUserDetail().getModeratorAppIdList())){
            isAdmin = true;
        }

        CscpUserDetail currentUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (currentUserDetail == null) {
            return Collections.emptyList();
        }

        // 构建查询条件
        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(orgName)) {
            lambdaQueryWrapper.like(CscpOrg::getOrgName, orgName);
        }
        if (orgId != null) {
            CscpOrg cscpOrg = this.getById(orgId);
            lambdaQueryWrapper.likeRight(CscpOrg::getOrgCodePath, cscpOrg.getOrgCodePath());
        }

        // 根据用户权限调整查询条件
        if (!isAdmin) {
            if (SecurityUtils.isUnitAdmin()) {
                lambdaQueryWrapper.likeRight(CscpOrg::getOrgCodePath, currentUserDetail.getOrgCodePath());
            } else {
                String orgIdPath = currentUserDetail.getOrgIdPath();
                if (StringUtils.isEmpty(orgIdPath)) {
                    return Collections.emptyList();
                }
                Set<Long> orgIds = Arrays.stream(orgIdPath.split("\\|"))
                        .filter(StringUtils::isNotEmpty)
                        .map(Long::valueOf)
                        .collect(Collectors.toSet());
                lambdaQueryWrapper.in(CscpOrg::getId, orgIds);
            }
        }

        // 查询匹配机构
        List<CscpOrg> matchedOrgs = cscpOrgRepository.selectListNoAdd(lambdaQueryWrapper);
        if (CollectionUtils.isEmpty(matchedOrgs)) {
            return Collections.emptyList();
        }

        // 保存原始搜索匹配到的机构ID，用于后续设置isValuable
        Set<Long> originalMatchedIds = matchedOrgs.stream().map(CscpOrg::getId).collect(Collectors.toSet());

        // 如果是管理员，收集匹配机构及其上级机构Id
        if (SecurityUtils.isAdmin() || SecurityUtils.isUnitAdmin()) {
            Set<Long> allIds = collectAllOrgIds(matchedOrgs);
            if (SecurityUtils.isUnitAdmin() && StringUtils.isNotEmpty(currentUserDetail.getOrgIdPath())) {
                for (String id : currentUserDetail.getOrgIdPath().split("\\|")) {
                    allIds.remove(Long.valueOf(id));
                }
            }
            if (orgId != null) {
                allIds.remove(orgId);
            }
            if (!allIds.isEmpty()) {
                lambdaQueryWrapper.clear();
                lambdaQueryWrapper.in(CscpOrg::getId, allIds);
                matchedOrgs = cscpOrgRepository.selectListNoAdd(lambdaQueryWrapper);
            }
        }

        // 转换为DTO并构建树结构
        Map<Long, CscpOrgDTO> nodeMap = new HashMap<>();
        List<CscpOrgDTO> orgDTOs = matchedOrgs.stream()
                .map(org -> {
                    CscpOrgDTO dto = new CscpOrgDTO();
                    BeanUtils.copyProperties(org, dto);
                    nodeMap.put(dto.getId(), dto);
                    return dto;
                })
                .collect(Collectors.toList());

        // 只对原始搜索匹配到的机构设置isValuable标识（判断是否有下级机构）
        if (!originalMatchedIds.isEmpty()) {
            LambdaQueryWrapper<CscpOrg> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(CscpOrg::getParentId, originalMatchedIds).select(CscpOrg::getParentId);
            List<CscpOrg> childOrgList = cscpOrgRepository.selectListNoAdd(wrapper);
            Set<Long> parentIds = childOrgList.stream().map(CscpOrg::getParentId).collect(Collectors.toSet());

            // 设置isValuable标识
            orgDTOs.forEach(dto -> {
                if (originalMatchedIds.contains(dto.getId())) {
                    // 只对原始搜索匹配到的机构设置isValuable
                    if (parentIds.contains(dto.getId())) {
                        dto.setIsValuable(1);  // 有下级机构
                    } else {
                        dto.setIsValuable(0);  // 无下级机构
                    }
                } else {
                    // 父级机构不设置isValuable，保持默认值
                    dto.setIsValuable(0);
                }
            });
        }

        // 构建树结构
        for (CscpOrgDTO orgDTO : orgDTOs) {
            Long parentId = orgDTO.getParentId();
            if (parentId != null && nodeMap.containsKey(parentId)) {
                CscpOrgDTO parentNode = nodeMap.get(parentId);
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList<>());
                }
                parentNode.getChildren().add(orgDTO);
            }
        }

        // 返回顶级节点
        return orgDTOs.stream()
                .filter(orgDTO -> orgDTO.getParentId() == null || orgDTO.getParentId() == 0 || !nodeMap.containsKey(orgDTO.getParentId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<CscpOrgDTO> selectOrgListById(Long id) {
        CscpOrg rootOrg = cscpOrgService.getById(id);
        LambdaQueryWrapper<CscpOrg> orgLqw = Wrappers.lambdaQuery();
        orgLqw.likeRight(CscpOrg::getOrgCodePath, rootOrg.getOrgCodePath());
        List<CscpOrg> list = cscpOrgService.selectListNoAdd(orgLqw);
        return this.generateTree(list);
    }

    @Override
    public List<CscpOrg> selectOrgStartWith(Long id) {
        return cscpOrgRepository.selectOrgStartWith(id);
    }

    @Override
    public List<CscpOrgDTO> selectAllChildNodesListById(List<Long> ids) {
        LambdaQueryWrapper<CscpOrg> orgLqw = new LambdaQueryWrapper<>();
        orgLqw.in(CscpOrg::getId, ids);
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(orgLqw);
        Set<String> codes = orgList.stream().map(CscpOrg::getOrgCodePath).collect(Collectors.toSet());
        List<CscpOrg> dataList = cscpOrgRepository.selectAllWithChildPaths(codes);
        return ListCopyUtil.copy(dataList, CscpOrgDTO.class);
    }

    @Override
    public List<CscpMainOrgVO> queryUnitOrgTree(Long parentId) {

        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(CscpOrg::getId,CscpOrg::getOrgName,CscpOrg::getParentId
                        , CscpOrg::getOrgCode, CscpOrg::getOrderBy, CscpOrg::getType)
                .eq(CscpOrg::getDeleted, 0)
                .eq(CscpOrg::getParentId, parentId)
                .orderByAsc(CscpOrg::getOrderBy);
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isEmpty(orgList)) {
            return Collections.emptyList();
        }

        List<Long> parentIds = orgList.stream().map(CscpOrg::getId).collect(Collectors.toList());
        List<CscpOrg> parentOrgList = cscpOrgRepository.selectListNoAdd(Wrappers.<CscpOrg>lambdaQuery().select(CscpOrg::getParentId)
                .eq(CscpOrg::getDeleted, 0).in(CscpOrg::getParentId, parentIds).groupBy(CscpOrg::getParentId));

        List<CscpMainOrgVO> list = ListCopyUtil.copy(orgList, CscpMainOrgVO.class);

        if (CollectionUtils.isNotEmpty(parentOrgList)) {
            Set<Long> has = parentOrgList.stream().map(CscpOrg::getParentId).collect(Collectors.toSet());
            for (CscpMainOrgVO cscpMainOrg : list) {
                cscpMainOrg.setHasChild(has.contains(cscpMainOrg.getId()));
            }
        }
        return list;
    }

    private List<CscpOrgDTO> generateTree(List<CscpOrg> list) {
        // 转换为DTO并构建树结构
        Map<Long, CscpOrgDTO> nodeMap = new HashMap<>();
        List<CscpOrgDTO> orgDTOs = list.stream()
                .map(org -> {
                    CscpOrgDTO dto = new CscpOrgDTO();
                    BeanUtils.copyProperties(org, dto);
                    nodeMap.put(dto.getId(), dto);
                    return dto;
                })
                .collect(Collectors.toList());

        // 构建树结构
        for (CscpOrgDTO orgDTO : orgDTOs) {
            Long parentId = orgDTO.getParentId();
            if (parentId != null && nodeMap.containsKey(parentId)) {
                CscpOrgDTO parentNode = nodeMap.get(parentId);
                if (parentNode.getChildren() == null) {
                    parentNode.setChildren(new ArrayList<>());
                }
                parentNode.getChildren().add(orgDTO);
            }
        }
        return orgDTOs;
    }

    // 提取收集所有机构Id的逻辑
    private Set<Long> collectAllOrgIds(List<CscpOrg> matchedOrgs) {
        Set<Long> allIds = new HashSet<>();
        for (CscpOrg org : matchedOrgs) {
            allIds.add(org.getId());
            if (StringUtils.isNotEmpty(org.getOrgIdPath())) {
                Arrays.stream(org.getOrgIdPath().split("\\|"))
                        .filter(StringUtils::isNotEmpty)
                        .map(Long::valueOf)
                        .forEach(allIds::add);
            }
        }
        return allIds;
    }


    /**
     * 更新错误的org_code
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateInvalidOrgCodes() {
        // 创建SQL日志文件
        String sqlLogPath = "update_org_codes_sql.log";
        try (PrintWriter writer = new PrintWriter(String.valueOf(new FileWriter(sqlLogPath, Charset.forName("UTF-8"))))) {
            writer.println("-- 以下是更新org_code的SQL语句，可以在其他数据库中执行");
            writer.println();

            // 1. 查找所有org_code等于id的记录
            QueryWrapper<CscpOrg> queryWrapper = new QueryWrapper<>();
            queryWrapper.apply("org_code =  CAST(id AS VARCHAR)");  // 直接使用SQL表达式
            List<CscpOrg> invalidOrgs = this.list(queryWrapper);

            if (CollectionUtils.isEmpty(invalidOrgs)) {
                return;
            }

            for (CscpOrg org : invalidOrgs) {
                // 2. 获取父级机构信息
                CscpOrg parentOrg = this.getById(org.getParentId());
                if (parentOrg == null) {
                    log.warn("未找到父级机构, orgId: {}", org.getId());
                    continue;
                }

                log.info("正在处理 orgId: {}, parentOrgId: {}", org.getId(), org.getParentId());
                // 3. 查询同级机构中最大的org_code
                QueryWrapper<CscpOrg> maxOrgCodeQuery = new QueryWrapper<>();
                maxOrgCodeQuery.eq("parent_id", org.getParentId());
                maxOrgCodeQuery.apply("org_code !=  CAST(id AS VARCHAR)")
                        .orderByDesc("org_code")
                        .last("LIMIT 1");
                CscpOrg maxOrgCodeOrg = this.getOne(maxOrgCodeQuery);

                // 4. 生成新的org_code
                String newOrgCode =org.getOrgCode();
                if (maxOrgCodeOrg == null) {
                    // 如果没有同级机构，基于父级org_code生成
                    newOrgCode = generateFirstOrgCode(parentOrg.getOrgCode());
                } else {
                    // 如果有同级机构，在最大值基础上+1
                    newOrgCode = generateNextOrgCode(maxOrgCodeOrg.getOrgCode());
                }

                // 5. 更新org_code
                String orgCode = org.getOrgCode();
                org.setOrgCode(newOrgCode);

                // 生成更新SQL并写入文件
                String updateSql = generateUpdateSql(org, orgCode);
                writer.println(updateSql);

                // 执行更新
                this.updateById(org);
            }

            writer.println();
            writer.println("-- SQL语句生成完成");
            log.info("更新SQL已保存到: {}", sqlLogPath);
        } catch (Exception e) {
            log.error("无法写入SQL日志文件", e);
        }
    }

    @Override
    public MainOrgVO queryMainOrgTree() {
        MainOrgVO orgVO = new MainOrgVO();
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CscpOrg::getType, 1,2).select(CscpOrg::getId,CscpOrg::getOrgName,CscpOrg::getParentId
                , CscpOrg::getOrgCode, CscpOrg::getOrderBy).eq(CscpOrg::getDeleted, 0).orderByAsc(CscpOrg::getOrderBy)
        ;
        List<CscpOrg> orgList = cscpOrgRepository.selectListNoAdd(queryWrapper);

        List<CscpMainOrgVO> copy = ListCopyUtil.copy(orgList , CscpMainOrgVO.class);

        List<Node<CscpMainOrgVO>> tree = mainBuildTree(copy);
        orgVO.setOrgTree(tree);
        return orgVO;
    }

    @Override
    public List<CscpOrgDTO> selectDivisionOrgByIdName(Long id, String orgName) {
        // 区域机构顶层
        List<CscpOrgDTO> roots = selectDivisionOrgById_new(id, null);
        if (CollectionUtils.isEmpty(roots)) {
            return new ArrayList<>();
        }
        List<String> rootOrgCodes = roots.stream().map(CscpOrgDTO::getOrgCode).collect(Collectors.toList());
        // 返回搜索树
        if (StringUtils.isEmpty(orgName)) {
            return roots;
        }
        List<CscpOrgDTO> cscpOrgList = getOrgWithAllParentsNewest(orgName, null, 1);
        if (CollectionUtils.isEmpty(cscpOrgList)) {
            return new ArrayList<>();
        }
        List<CscpOrgDTO> result = new ArrayList<>();

        add4Tree(result, cscpOrgList, rootOrgCodes);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrgOrderBy(List<CscpOrgDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("参数不能为空");
        }

        dataList = dataList.stream().sorted(Comparator.comparing(CscpOrgDTO::getOrderBy))
                .collect(Collectors.toList());
        Map<Long, Integer> map = dataList.stream().collect(Collectors.toMap(CscpOrgDTO::getId, CscpOrgDTO::getOrderBy));
        LambdaQueryWrapper<CscpOrg> orgLqw = Wrappers.lambdaQuery();
        orgLqw.in(CscpOrg::getId, dataList.stream().map(CscpOrgDTO::getId).collect(Collectors.toList()));
        List<CscpOrg> orgList = cscpOrgService.selectListNoAdd(orgLqw);

        SecurityContext securityContext = SecurityContextHolder.getContext();
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (CscpOrg org : orgList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(securityContext);
                Integer orderBy = map.get(org.getId());
                if (null == orderBy) {
                    throw new BusinessException("排序不能为空");
                }
                if (orderBy > 99999) {
                    throw new BusinessException("排序不能超过99999");
                }

                LambdaUpdateWrapper<CscpOrg> updateLuw = Wrappers.lambdaUpdate();
                updateLuw.eq(CscpOrg::getId, org.getId());
                updateLuw.set(CscpOrg::getOrderBy, orderBy);
                cscpOrgRepository.update(null, updateLuw);
            });
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

    }

    private void add4Tree(List<CscpOrgDTO> result, List<CscpOrgDTO> cscpOrgList, List<String> rootOrgCodes) {
        if (CollectionUtils.isNotEmpty(cscpOrgList)) {
            for (CscpOrgDTO orgDTO : cscpOrgList) {
                if (rootOrgCodes.contains(orgDTO.getOrgCode())) {
                    result.add(orgDTO);
                }
                add4Tree(result, orgDTO.getChildren(), rootOrgCodes);
            }
        }
    }

    private List<Node<CscpMainOrgVO>> mainBuildTree(List<CscpMainOrgVO> orgDTOList) {
        List<Node<CscpMainOrgVO>> ret = Lists.newArrayList();
        Map<Long, Node<CscpMainOrgVO>> nodeMap = Maps.newHashMap();
        for (CscpMainOrgVO orgDTO : orgDTOList){
            Node<CscpMainOrgVO> node = new Node<>();
            node.setId(orgDTO.getId());
            node.setTitle(orgDTO.getOrgName());
            node.setDetailsData(orgDTO);
            nodeMap.put(node.getId(), node);
        }
        for (CscpMainOrgVO cscpOrgDTO : orgDTOList){
            Long parentId = cscpOrgDTO.getParentId();
            Node<CscpMainOrgVO> parentNode = nodeMap.get(parentId);
            if (parentNode == null){
                continue;
            }
            List<Node<CscpMainOrgVO>> children = parentNode.getChildren();
            if (children == null){
                children = Lists.newArrayList();
                parentNode.setChildren(children);
            }
            children.add(nodeMap.get(cscpOrgDTO.getId()));
        }
        // 找到顶级节点 放到返回对象里面
        for (Map.Entry<Long, Node<CscpMainOrgVO>> entry : nodeMap.entrySet()){
            Node<CscpMainOrgVO> node = entry.getValue();
            CscpMainOrgVO cscpOrgDTO = node.getDetailsData();
            if (cscpOrgDTO.getParentId() == null || cscpOrgDTO.getParentId().equals(0L)){
                ret.add(node);
            }
        }
        // 机构列表排序
        Collections.sort(ret, new Comparator<Node<CscpMainOrgVO>>() {
            @Override
            public int compare(Node<CscpMainOrgVO> o1, Node<CscpMainOrgVO> o2) {
                return o1.getDetailsData().getOrderBy() - o2.getDetailsData().getOrderBy();
            }
        });
        return ret;

    }

    /**
     * 生成更新SQL语句
     */
    private String generateUpdateSql(CscpOrg org,String oldOrgCode) {
        return String.format("UPDATE cscp_org SET org_code = '%s' WHERE id = %s; -- 原org_code: %s",
                org.getOrgCode(), org.getId(), oldOrgCode);
    }

    /**
     * 生成第一个子机构的org_code
     */
    private String generateFirstOrgCode(String parentOrgCode) {
        return parentOrgCode + "0001";
    }

    /**
     * 生成下一个org_code
     */
    private String generateNextOrgCode(String currentMaxOrgCode) {
        try {
            // 获取数字部分
            String numericPart = currentMaxOrgCode.substring(currentMaxOrgCode.length() - 3);
            int nextNum = Integer.parseInt(numericPart) + 1;

            // 格式化为3位数字，不足补0
            String formattedNum = String.format("%03d", nextNum);

            // 返回父级部分+新的数字
            return currentMaxOrgCode.substring(0, currentMaxOrgCode.length() - 3) + formattedNum;
        } catch (Exception e) {
            log.error("生成org_code失败", e);
            throw new RuntimeException("生成org_code失败");
        }
    }
    /**
     * 递归查询扁平机构树
     * @param id
     * @return
     */
    @Override
    public List<CscpOrgDTO> selectOrgTreeByParentId(Long id) {
        if (id == 0) {
            return cscpOrgRepository.getAllOrgsFlat(); // 查询全部
        } else {
            return cscpOrgRepository.getFlatOrgList(id); // 查询某个节点下所有子机构
        }
    }

    @Override
    public List<CscpOrg> selectChildrenByCondition(Long orgId, List<String> orgNameList) {
        return cscpOrgRepository.selectChildrenByCondition(orgId, orgNameList);
    }

    /**
     * 根据机构查询对应机构数据
     * @param orgCode
     * @return
     */
    @Override
    public CscpOrgDTO selectOrgCode(String orgCode) {
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CscpOrg::getOrgCode, orgCode);
        CscpOrg cscpOrg = cscpOrgRepository.selectOneNoAdd(queryWrapper);
        return BeanUtil.toBean(cscpOrg,CscpOrgDTO.class);
    }

    /**
     * 根据parentId查询数据，并且判断是否有下一级
     */
    @Override
    public List<CscpOrgDTO> selectOrgByParentIdAddressBook(Long parentId) {
        LambdaQueryWrapper<CscpOrg> cscpOrgLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cscpOrgLambdaQueryWrapper.eq(CscpOrg::getParentId,parentId);
        cscpOrgLambdaQueryWrapper.orderByAsc(CscpOrg::getOrderBy);
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(cscpOrgLambdaQueryWrapper);
        if (CollUtil.isEmpty(cscpOrgList)){
            return ListUtil.empty();
        }
        List<CscpOrgDTO> cscpOrgDTOList = ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
        List<Long> ids = cscpOrgList.stream().map(CscpOrg::getId).collect(Collectors.toList());
        LambdaQueryWrapper<CscpOrg> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CscpOrg::getParentId,ids).select(CscpOrg::getParentId);
        List<CscpOrg> selectListNoAdd = cscpOrgRepository.selectListNoAdd(wrapper);
        List<Long> parentIds = selectListNoAdd.stream().map(CscpOrg::getParentId).distinct().collect(Collectors.toList());
        //判断是否有下级并设置标识
        cscpOrgDTOList.forEach(i -> {
            if (parentIds.contains(i.getId())) {
                i.setIsValuable(1);
            } else {
                i.setIsValuable(0);
            }
            if (StrUtil.isNotBlank(i.getAliasName())){
                i.setOrgName(i.getAliasName());
            }
        });

        return cscpOrgDTOList;
    }

    @Override
    public Map<String, Object> getUserAdminAndCreditCodeStatus() {
        Map<String, Object> result = new HashMap<>();

        // 获取当前用户单位ID
        Long companyId = SecurityUtils.getCurrentCompanyId();

        // 判断当前用户是否为管理员
        if (SecurityUtils.isUnitAdmin()) {
            // 如果是管理员，查询当前单位的统一社会信用代码
            CscpOrg orgInfo = cscpOrgRepository.selectById(companyId);
            String creditCode = orgInfo != null ? orgInfo.getCreditCode() : "";
            Integer type = orgInfo != null ? orgInfo.getType() : null;

            if (type != null && type == 2) {
                // 只有type=2时才需要creditCode返回true
                if (StringUtils.isEmpty(creditCode)) {
                    result.put("isUnitAdmin", true);
                    result.put("creditCode", true);
                } else {
                    result.put("isUnitAdmin", true);
                    result.put("creditCode", false);
                }
            } else {
                result.put("isUnitAdmin", true);
                result.put("creditCode", false);
            }
        } else {
            result.put("isUnitAdmin", false);
            result.put("creditCode", false);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateCreditCode(String creditCode) {
        // 获取当前用户单位ID
        Long companyId = SecurityUtils.getCurrentCompanyId();

        // 验证当前用户是否为管理员
        if (!SecurityUtils.isUnitAdmin()) {
            throw new BusinessException("当前用户不是单位管理员，无权限更新统一社会信用代码");
        }

        // 验证统一社会信用代码格式（可选）
        if (StringUtils.isEmpty(creditCode)) {
            throw new BusinessException("统一社会信用代码不能为空");
        }

        // 更新统一社会信用代码
        CscpOrg updateOrg = new CscpOrg();
        updateOrg.setId(companyId);
        updateOrg.setCreditCode(creditCode);

        int updateResult = cscpOrgRepository.updateById(updateOrg);
        return updateResult > 0;
    }

    @Override
    public void updateAppCodesById(Long orgId, String appCodes) {
        cscpOrgRepository.updateAppCodesById(orgId, appCodes);
    }

    @Override
    public List<CscpOrgDTO> selectOrgRegionCityList() {
        LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ge(CscpOrg::getOrgCode, "4301")  // >= 4301
                .lt(CscpOrg::getOrgCode, "4400")  // < 4400
                .likeLeft(CscpOrg::getOrgCode, "00000000")  // 以00000000结尾
                .apply("LENGTH(org_code) = 12")
                .apply("SUBSTRING(org_code, 3, 2) != '00'")
                .orderByAsc(CscpOrg::getOrderBy);

        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(queryWrapper);
        return ListCopyUtil.copy(cscpOrgList, CscpOrgDTO.class);
    }

    @Override
    public Long getUnitIdByOrgId(Long orgId) {
        CscpOrg cscpOrg = cscpOrgRepository.selectById(orgId);
        if (cscpOrg == null) {
            return orgId;
        }

        // 如果是单位，直接返回
        if (cscpOrg.getType() == 2) {
            return cscpOrg.getId();
        }

        CscpOrg parentOrg = cscpOrgRepository.selectById(cscpOrg.getParentId());
        if (parentOrg != null && parentOrg.getType() == 2) {
            return parentOrg.getId();
        }

        return orgId;
    }

    @Override
    public boolean checkCreditCode(Long id,String creditCode) {
        // 信用代码为空时直接通过校验
        if (StringUtils.isEmpty(creditCode)) {
            return true;
        }

        LambdaQueryWrapper<CscpOrg> queryWrapper = Wrappers.lambdaQuery(CscpOrg.class)
                .eq(CscpOrg::getCreditCode, creditCode);

        // 修改场景：排除当前记录自身（通过ID）
        if (id != null) {
            queryWrapper.ne(CscpOrg::getId, id);
        }

        int count = cscpOrgRepository.selectCountNoAdd(queryWrapper);
        return count == 0; // 存在重复则返回false，否则返回true
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLocateAppCodeBatch(List<Long> ids, String appCode) {
        cscpOrgRepository.updateLocateAppCodeBatch(ids, appCode);
    }


    /**
     * 查询指定内设机构所属的单位信息，并设置单位ID和统一社会信用代码到传入的cscpOrg对象中。
     * 若未找到或查询异常，则清空相关字段并记录日志。
     *
     * @param cscpOrg 内设机构对象，type应为3
     */
    @Override
    public void handleOrgBelongCompanyInfo(CscpOrg cscpOrg) {
        if (!ObjectUtil.equal(cscpOrg.getType(), 3)) {
            return;// 只有内设机构才执行
        }
        if(cscpOrg.getOrgBelongCompanyId()!=null){
            return; // 如果已经有所属单位信息，则不再处理
        }

        try {
            CscpOrg companyInfo = cscpOrgRepository.getCompanyInfoByDepartmentId(cscpOrg.getId());
            if (companyInfo != null) {
                cscpOrg.setOrgBelongCompanyId(companyInfo.getId());
                cscpOrg.setOrgBelongCompanyCreditCode(companyInfo.getCreditCode());
            } else {
                log.warn("未能找到部门ID为{}的所属单位信息", cscpOrg.getId());
                cscpOrg.setOrgBelongCompanyId(null);
                cscpOrg.setOrgBelongCompanyCreditCode(null);
            }
        } catch (Exception e) {
            log.error("查询部门ID为{}所属单位时发生异常", cscpOrg.getId(), e);
            cscpOrg.setOrgBelongCompanyId(null);
            cscpOrg.setOrgBelongCompanyCreditCode(null);
        }
    }

    @Override
    public List<CscpOrg> selectOrgHasDeleted(List<Long> ids) {
        return cscpOrgRepository.selectOrgHasDeleted(ids);
    }

}

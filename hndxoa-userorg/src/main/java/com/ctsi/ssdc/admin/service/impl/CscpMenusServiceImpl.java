package com.ctsi.ssdc.admin.service.impl;


import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.constant.RedisKeyConstant;
import com.ctsi.hndx.datarule.entity.CscpDataRule;
import com.ctsi.hndx.datarule.mapper.CscpDataRuleMapper;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.mybatisplus.sort.SortEnum;
import com.ctsi.hndx.systenant.entity.dto.TSysTenantDTO;
import com.ctsi.hndx.systenant.service.ITSysTenantService;
import com.ctsi.hndx.systenant.service.ITTenantMenuService;
import com.ctsi.hndx.tree.impl.BaseTreeCurdServiceImpl;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.GzipUtil;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.admin.domain.CscpMenus;
import com.ctsi.ssdc.admin.domain.CscpRoleMenu;
import com.ctsi.ssdc.admin.domain.CscpRoles;
import com.ctsi.ssdc.admin.domain.SysMenusCustomization;
import com.ctsi.ssdc.admin.domain.dto.CscpDragDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpMenusDTO;
import com.ctsi.ssdc.admin.domain.dto.SysMenusCustomizationDTO;
import com.ctsi.ssdc.admin.entity.CscpSanyuanMenus;
import com.ctsi.ssdc.admin.mapper.CscpSanyuanMenusMapper;
import com.ctsi.ssdc.admin.mapping.CscpMenusMapping;
import com.ctsi.ssdc.admin.repository.CscpMenusRepository;
import com.ctsi.ssdc.admin.repository.CscpRoleMenuRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRoleRepository;
import com.ctsi.ssdc.admin.repository.SysMenusCustomizationMapper;
import com.ctsi.ssdc.admin.service.CscpMenusService;
import com.ctsi.ssdc.admin.service.ISysMenusCustomizationService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import com.ctsi.ssdc.security.jwt.TokenProvider;
import com.ctsi.ssdc.util.RedisUtil;
import com.google.common.cache.LoadingCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing CscpMenus.
 *
 * <AUTHOR> biyi generator
 */
@Service
public class CscpMenusServiceImpl extends BaseTreeCurdServiceImpl<CscpMenusDTO, CscpMenus, CscpMenusRepository>
        implements CscpMenusService {
    private final Logger log = LoggerFactory.getLogger(CscpMenusServiceImpl.class);


    @Autowired
    private CscpMenusRepository cscpMenusRepository;

    @Autowired
    private CscpSanyuanMenusMapper cscpSanyuanMenusMapper;


    @Autowired
    private CscpRoleMenuRepository cscpRoleMenuRepository;

    @Autowired
    private CscpDataRuleMapper cscpDataRuleMapper;

    @Autowired
    private CscpUserRoleRepository cscpUserRoleRepository;

    @Autowired
    private ITTenantMenuService itTenantMenuService;

    @Autowired
    private TokenProvider tokenProvider;

    @Autowired
    private ITSysTenantService itSysTenantService;

    @Autowired
    private ISysMenusCustomizationService sysMenusCustomizationService;

    @Autowired
    private SysMenusCustomizationMapper sysMenusCustomizationMapper;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CscpMenusMapping menusMapping;

    private static final Integer ONLY_ADMIN_USED = 2;

    private static final String MENUS_BUTTON_TYPE = "button";

    private static final Long ROOT_MENU_ID = 0L;

    /**
     * insert a cscpMenus.
     *
     * @param cscpMenusDTO the entity to insert
     * @return the persisted entity
     */

   /* public CscpMenusDTO insert(CscpMenusDTO cscpMenusDTO) {
        log.debug("Request to insert CscpMenus : {}", cscpMenusDTO);

        CscpMenus cscpMenus = cscpMenusMapper.toEntity(cscpMenusDTO);
        cscpMenusRepository.insert(cscpMenus);
        int id = cscpMenus.getId();
        CscpMenus  cscpMenusOrder = new CscpMenus();
        cscpMenusOrder.setOrderby(id);
        CscpMenusExample menusExample =new CscpMenusExample();
        Criteria criteria = menusExample.createCriteria();
        criteria.andIdEqualTo(id);
        cscpMenusRepository.updateByExampleSelective(cscpMenusOrder,menusExample);
        return cscpMenusMapper.toDto(cscpMenus);
    }*/
    @Override
    //重写insert方法
    public CscpMenusDTO insert(CscpMenusDTO cscpMenusDTO) {
        log.debug("Request to insert CscpMenus : {}", cscpMenusDTO);
        CscpMenus cscpMenus = new CscpMenus();
        BeanUtils.copyProperties(cscpMenusDTO, cscpMenus);
        //获取orderby顺序
        cscpMenus.setOrderBy(getOrderBy(cscpMenusDTO));
        cscpMenusRepository.insert(cscpMenus);
        BeanUtils.copyProperties(cscpMenus, cscpMenusDTO);
        return cscpMenusDTO;
    }

    //获取orderby顺序
    @Override
    public int getOrderBy(CscpMenusDTO cscpMenusDTO) {
        CscpMenus cscpMenus = new CscpMenus();
        BeanUtils.copyProperties(cscpMenusDTO, cscpMenus);
        int orderById;
        long parent_id = cscpMenus.getParentId();
        //获取相同parent_id的孩子数量
        List<CscpMenus> childs = cscpMenusRepository.selectByParentId(parent_id);
        if (childs.isEmpty()) {
            orderById = 1;
        } else {
            //最后一个孩子的顺序值
            orderById = childs.get(childs.size() - 1).getOrderBy() + 1;
        }

        return orderById;
    }

    /**
     * update a cscpMenus.
     *
     * @param cscpMenusDTO the entity to update
     * @return the persisted entity
     */
    @Override
    public CscpMenusDTO update(CscpMenusDTO cscpMenusDTO) {
        log.debug("Request to update CscpMenus : {}", cscpMenusDTO);

        CscpMenus cscpMenus = new CscpMenus();
        BeanUtils.copyProperties(cscpMenusDTO, cscpMenus);
        cscpMenusRepository.updateById(cscpMenus);
        BeanUtils.copyProperties(cscpMenus, cscpMenusDTO);
        return cscpMenusDTO;
    }


    /**
     * Get all the cscpMenuss.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public PageResult<CscpMenusDTO> findAll() {
        log.debug("Request to get all CscpMenuss");

//        List<CscpMenusDTO> data = cscpMenusRepository.selectList(null).stream()
//                .map(cscpMenusMapper::toDto)
//                .collect(Collectors.toCollection(LinkedList::new));
        List<CscpMenusDTO> data = ListCopyUtil.copy(cscpMenusRepository.selectList(null),
                CscpMenusDTO.class);

        long count = 0L;

        if (CollectionUtils.isNotEmpty(data)) {
            count = cscpMenusRepository.selectCount(null);
        }

        return new PageResult<CscpMenusDTO>(data, count, count);

    }

    @Override
    public List<CscpMenus> findAllList() {
        log.debug("Request to get all CscpMenuss");
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();
        // 如果登录的不是平台管理员帐号，需要去掉平台管理员专用的菜单
        if (SecurityUtils.isAuthenticated() && !SecurityUtils.isSystemName()) {
            queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
        }
        return cscpMenusRepository.selectList(queryWrapper);
    }

    /**
     * Get one cscpMenus.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public CscpMenusDTO findOne(Long id) {
        log.debug("Request to get CscpMenus : {} ", id);

        CscpMenus cscpMenus = cscpMenusRepository.selectById(id);
        if (cscpMenus != null) {
            CscpMenusDTO dto = new CscpMenusDTO();
            BeanUtils.copyProperties(cscpMenus, dto);
            return dto;
        } else {
            return null;
        }
    }

    /**
     * Delete the cscpMenus .
     * 递归删除节点下面所有子节点
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete CscpMenus : {} ", id);
        List<CscpMenus> cscpMenus = cscpMenusRepository.selectByParentId(id);
        if (!cscpMenus.isEmpty()) {
            for (CscpMenus cscpMenu : cscpMenus) {
                delete(cscpMenu.getId());
            }
        }
        cscpMenusRepository.deleteById(id);
    }


    /**
     * Get the cscpMenuss.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public PageResult<CscpMenusDTO> findByCscpMenusDTO(CscpMenusDTO cscpMenusDTO, BasePageForm basePageForm) {

        log.debug("Request to find CscpMenuss");
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();


        if (cscpMenusDTO.getId() != null) {
            queryWrapper.eq(CscpMenus::getId, cscpMenusDTO.getId());
        }
        if (cscpMenusDTO.getParentId() != null) {
            queryWrapper.eq(CscpMenus::getParentId, cscpMenusDTO.getParentId());
        }
        if (cscpMenusDTO.getOrderBy() != null) {
            //  queryWrapper.orderByDesc(CscpMenus::getOrderby, cscpMenusDTO.getOrderby());;
        }

        if (StringUtils.isNotBlank(cscpMenusDTO.getName())) {
            queryWrapper.like(CscpMenus::getName, cscpMenusDTO.getName());

        }
        if (StringUtils.isNotBlank(cscpMenusDTO.getIcon())) {
            queryWrapper.like(CscpMenus::getIcon, cscpMenusDTO.getIcon());
        }
        if (StringUtils.isNotBlank(cscpMenusDTO.getTitle())) {
            queryWrapper.like(CscpMenus::getTitle, cscpMenusDTO.getTitle());
        }
        if (StringUtils.isNotBlank(cscpMenusDTO.getUrl())) {
            queryWrapper.like(CscpMenus::getUrl, cscpMenusDTO.getUrl());
        }
        if (StringUtils.isNotBlank(cscpMenusDTO.getComponent())) {
            queryWrapper.like(CscpMenus::getComponent, cscpMenusDTO.getComponent());
        }
        if (StringUtils.isNotBlank(cscpMenusDTO.getType())) {
            queryWrapper.like(CscpMenus::getType, cscpMenusDTO.getType());
        }

        IPage<CscpMenus> userIPage = cscpMenusRepository.selectPage(PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);

        List<CscpMenusDTO> data = ListCopyUtil.copy(userIPage.getRecords(), CscpMenusDTO.class);

        return new PageResult<CscpMenusDTO>(data, userIPage.getTotal(), userIPage.getTotal());

    }


    @Override
    public List<CscpMenusDTO> findByUserId(Long id) {
        if (Arrays.asList(1L, 3L).contains(id)) {
            // 保密和审计管理员走数据库查询
            List<CscpSanyuanMenus> cscpSanyuanMenusList = cscpSanyuanMenusMapper.selectListNoAdd(Wrappers.<CscpSanyuanMenus>lambdaQuery().eq(CscpSanyuanMenus::getUserId, id).orderByAsc(CscpSanyuanMenus::getOrderBy));
            return menusMapping.convertCscpMenusList(cscpSanyuanMenusList);
        } else {
            // 从redis里面获取菜单数据
            List<CscpMenusDTO> cscpMenusDTOList = this.getMenuCache(id);

            // 如果redis里面存在，则直接返回
            if (CollectionUtils.isEmpty(cscpMenusDTOList)) {
                cscpMenusDTOList = findByUserIdNoUserCache(id);
            }
            List<SysMenusCustomization> list = this.getMenuCustomizationCache();
            if (list != null && list.size() > 0) {
                cscpMenusDTOList.forEach(item -> {
                    List<SysMenusCustomization> mcList = list.stream().filter(q -> q.getMenuId().longValue() == item.getId().longValue()).collect(Collectors.toList());
                    if (mcList != null && mcList.size() > 0) {
                        item.setTitle(mcList.get(0).getTitle());
                    }
                });
            }
            if (id == 2 || SecurityUtils.isAdmin()) {
                // 如果是系统管理员 取菜单和三元菜单并集
                List<CscpSanyuanMenus> cscpSanyuanMenusList = cscpSanyuanMenusMapper.selectListNoAdd(Wrappers.<CscpSanyuanMenus>lambdaQuery().eq(CscpSanyuanMenus::getUserId, id).orderByAsc(CscpSanyuanMenus::getOrderBy));
                List<CscpMenusDTO> cscpMenusList = menusMapping.convertCscpMenusList(cscpSanyuanMenusList);

                // 创建cscpMenusDTOList中菜单ID的集合，用于快速查找
                Set<Long> cscpMenusDTOIdSet = cscpMenusDTOList.stream()
                        .map(CscpMenusDTO::getId)
                        .collect(Collectors.toSet());

                // 保留cscpMenusList中不在cscpMenusDTOList中的菜单项
                List<CscpMenusDTO> newMenus = cscpMenusList.stream()
                        .filter(menu -> !cscpMenusDTOIdSet.contains(menu.getId()))
                        .collect(Collectors.toList());

                // 将这些新菜单项添加到cscpMenusDTOList中
                cscpMenusDTOList.addAll(newMenus);

                // 排序
                cscpMenusDTOList = cscpMenusDTOList.stream()
                        .sorted(Comparator.comparing(CscpMenusDTO::getOrderBy))
                        .collect(Collectors.toList());
            }
            return cscpMenusDTOList;
        }
    }

    @Override
    public void saveCscpSanyuanMenus(Long userId) {
        // 获取用户菜单列表
        List<CscpSanyuanMenus> cscpSanyuanMenusList = cscpSanyuanMenusMapper.selectListNoAdd(Wrappers.<CscpSanyuanMenus>lambdaQuery().eq(CscpSanyuanMenus::getUserId, userId));
        Map<Long, CscpSanyuanMenus> sanyuanMenusMap = cscpSanyuanMenusList.stream().collect(Collectors.toMap(CscpSanyuanMenus::getMenuId, Function.identity()));
        // 查询用户角色ID
        List<Long> roleIds = cscpUserRoleRepository.queryRoleByUserId(userId).stream().map(
                i -> i.getId()
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        // 查询角色管理的菜单ID
        LambdaQueryWrapper<CscpRoleMenu> cscpRoleMenuLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cscpRoleMenuLambdaQueryWrapper.in(CscpRoleMenu::getRoleId, roleIds);
        List<Long> menuIds = cscpRoleMenuRepository.selectList(cscpRoleMenuLambdaQueryWrapper).stream().map(
                CscpRoleMenu::getMenuId
        ).distinct().collect(Collectors.toList());

        // 查询角色关联的菜单信息
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(CscpMenus::getId, menuIds);
        queryWrapper.orderByAsc(CscpMenus::getOrderBy);
        List<CscpMenus> list = cscpMenusRepository.selectList(queryWrapper);
        list.forEach(cscpMenus -> {
            CscpSanyuanMenus sanyuanMenus = menusMapping.convertCscpSanyuanMenus(cscpMenus, userId);
            if (sanyuanMenusMap.get(cscpMenus.getId()) == null) {
                cscpSanyuanMenusMapper.insert(sanyuanMenus);
            } else {
                cscpSanyuanMenusMapper.updateById(sanyuanMenus);
            }
        });
    }


    @Override
    public List<CscpMenusDTO> findByUserIdNoUserCache(Long id) {

        // 查询用户角色ID
        List<Long> roleIds = cscpUserRoleRepository.queryRoleByUserId(id).stream().map(
                i -> i.getId()
        ).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return new ArrayList<>();
        }

        // 查询角色管理的菜单ID
        LambdaQueryWrapper<CscpRoleMenu> cscpRoleMenuLambdaQueryWrapper = new LambdaQueryWrapper<>();
        cscpRoleMenuLambdaQueryWrapper.in(CscpRoleMenu::getRoleId, roleIds);
        List<Long> menuIds = cscpRoleMenuRepository.selectList(cscpRoleMenuLambdaQueryWrapper).stream().map(
                i -> i.getMenuId()
        ).distinct().collect(Collectors.toList());

        // 查询角色关联的菜单信息
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(CscpMenus::getId, menuIds);

        // 如果登录的不是平台管理员帐号，需要去掉平台管理员专用的菜单
        if (SecurityUtils.isAuthenticated() && !SecurityUtils.isSystemName()) {
            queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
        }
        queryWrapper.orderByAsc(CscpMenus::getOrderBy);
        List<CscpMenus> list = cscpMenusRepository.selectList(queryWrapper);
        List<CscpMenusDTO> menusInRoleList = this.assembledCustomizeList(ListCopyUtil.copy(list, CscpMenusDTO.class));

        // 如果登录的账号是普通用户，需要取租户和角色两者关联的菜单的交集
        if (SecurityUtils.isAuthenticated() && SecurityUtils.isGeneralName()) {
            CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            Long tenantId = cscpUserDetail.getTenantId();

            // 如果该普通账号所在的租户设置了不受PC端权限控制，则直接返回其角色关联的菜单
            TSysTenantDTO tenantDTO = itSysTenantService.findOne(tenantId);
            if (Objects.nonNull(tenantDTO.getPcAuthority()) && !tenantDTO.getPcAuthority()) {
                // 设置缓存需要登录后才可以
                if (SecurityUtils.isAuthenticated()) {
                    tokenProvider.setMenuCache(id, menusInRoleList);
                }
                return menusInRoleList;
            }

            // 获取租户关联的菜单
            List<CscpMenusDTO> menusInTenantList = this.assembledCustomizeList(itTenantMenuService.queryListByTenantId(tenantId));

            // 两者取交集
            List<CscpMenusDTO> cscpMenusDTOS = menusInTenantList.stream()
                    .filter(item -> menusInRoleList.stream().map(e -> e.getId())
                            .collect(Collectors.toList()).contains(item.getId()))
                    .collect(Collectors.toList());
            if (SecurityUtils.isAuthenticated()) {
                tokenProvider.setMenuCache(id, cscpMenusDTOS);
            }
            return cscpMenusDTOS;
        }
        /*if (SecurityUtils.isAuthenticated()) {
            tokenProvider.setMenuCache(id, menusInRoleList);
        }*/
        tokenProvider.setMenuCache(id, menusInRoleList);
        return menusInRoleList;
    }

    public List<CscpMenusDTO> findByUserIdNoUserCache2(Long id) {
        boolean isAuthenticated = SecurityUtils.isAuthenticated();

        // 批量查出菜单ID
        List<Long> menuIds = cscpUserRoleRepository.selectMenuIdsByUserIds(id);
        if (menuIds.isEmpty()) {
            return Collections.emptyList();
        }

        // 构造查询条件
        LambdaQueryWrapper<CscpMenus> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(CscpMenus::getId, menuIds);

        if (isAuthenticated && !SecurityUtils.isSystemName()) {
            queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
        }
        queryWrapper.orderByAsc(CscpMenus::getOrderBy);

        List<CscpMenus> rawMenuList = cscpMenusRepository.selectList(queryWrapper);
        List<CscpMenusDTO> menusInRoleRaw = ListCopyUtil.copy(rawMenuList, CscpMenusDTO.class);


        List<CscpMenusDTO> menusInRoleList = this.assembledCustomizeList(menusInRoleRaw);

        // 如果是普通用户，取租户菜单和角色菜单的交集
        if (isAuthenticated && SecurityUtils.isGeneralName()) {
            CscpUserDetail userDetail = SecurityUtils.getCurrentCscpUserDetail();
            Long tenantId = userDetail.getTenantId();
            if (tenantId == null) {
                tokenProvider.setMenuCache(id, menusInRoleList);
                return menusInRoleList;
            }

            TSysTenantDTO tenantDTO = itSysTenantService.findOne(tenantId);
            if (tenantDTO != null && Boolean.FALSE.equals(tenantDTO.getPcAuthority())) {
                tokenProvider.setMenuCache(id, menusInRoleList);
                return menusInRoleList;
            }

            // 获取租户菜单并组装自定义配置
            List<CscpMenusDTO> tenantMenuList = Optional.ofNullable(itTenantMenuService.queryListByTenantId(tenantId))
                    .orElse(Collections.emptyList());

            List<CscpMenusDTO> menusInTenantList = this.assembledCustomizeList(tenantMenuList);

            Set<Long> roleMenuIds = menusInRoleList.stream()
                    .map(CscpMenusDTO::getId)
                    .collect(Collectors.toSet());

            List<CscpMenusDTO> intersection = menusInTenantList.stream()
                    .filter(item -> roleMenuIds.contains(item.getId()))
                    .collect(Collectors.toList());

            tokenProvider.setMenuCache(id, intersection);
            return intersection;
        }

        tokenProvider.setMenuCache(id, menusInRoleList);
        return menusInRoleList;
    }

    @Override
    public List<Tree<String>> getMenuTreeByRoleId(Long roleId) {
        // 获取所有菜单列表
        List<CscpMenusDTO> allMenus = new ArrayList<>();
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();
        // 平台去掉租户的
        String strRoleId = String.valueOf(roleId);
        if (SystemRole.SYSTEM_ROLE.getId().equals(strRoleId)) {
            queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_TENANT_USE);
        } else if (SystemRole.TENANT_ROLE.getId().equals(strRoleId)) {
            // 租户去掉平台的
            queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
        } else {
            queryWrapper.eq(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ALL_USE);

        }
        // 如果是单位管理员，可以查看全部菜单
        if (SecurityUtils.isSystemName()) {
            allMenus = ListCopyUtil.copy(cscpMenusRepository.selectList(queryWrapper), CscpMenusDTO.class);
        } else if (SecurityUtils.isUnitAdmin()) {
            allMenus = findByUserId(SecurityUtils.getCurrentUserId());
        } else {
            // 如果是其他用户，则只能查询其所在租户所拥有的菜单
            CscpUserDetail currentCscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            // 如果该用户所在租户设置了不受PC端权限控制，则查询除平台管理员专用的菜单外的所有菜单
            TSysTenantDTO tenantDTO = itSysTenantService.findOne(currentCscpUserDetail.getTenantId());
            if (Objects.nonNull(tenantDTO.getPcAuthority()) && !tenantDTO.getPcAuthority()) {
                queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
                allMenus = ListCopyUtil.copy(cscpMenusRepository.selectList(queryWrapper), CscpMenusDTO.class);
            } else {
                allMenus = itTenantMenuService.queryListByTenantId(currentCscpUserDetail.getTenantId());
            }
        }

        // 组装单位自定义菜单列表
        allMenus = this.assembledCustomizeList(allMenus);

        // 获取角色关联的菜单ID列表
        Set<Long> roleRelMenuIdSet = this.getMenuListByRoleId(roleId).stream().map(i -> i.getId()).collect(Collectors.toSet());

        // 构建的整个树数据
        List<Tree<String>> treeNodes = this.assembledTreeNodeList(allMenus, roleRelMenuIdSet);
        return treeNodes;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveRoleMenus(Long roleId, String menus, String permissions) {
        String[] ms = menus.split(",");

        try {
            LambdaQueryWrapper<CscpRoleMenu> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(CscpRoleMenu::getRoleId, roleId);
            cscpRoleMenuRepository.delete(queryWrapper);

            Long systemRoleId = Long.parseLong(SystemRole.SYSTEM_ROLE.getId());
            for (int i = 0; i < ms.length; i++) {
                Long menuId = 1L;
                // 兼容通知公告发送权限
                if (StrUtil.isNotBlank(ms[i])) {
                    CscpMenusDTO cscpMenusDTO = this.findOne(Long.valueOf(ms[i]));
                    if (ONLY_ADMIN_USED.equals(cscpMenusDTO.getPlatformAdminUse())
                            && !systemRoleId.equals(roleId)) {
                        throw new BusinessException(cscpMenusDTO.getName() + " 菜单只能给平台管理员使用");
                    }
                    menuId = Long.valueOf(ms[i]);
                }
                CscpRoleMenu record = new CscpRoleMenu();
                record.setRoleId(roleId);
                record.setMenuId(menuId);
                cscpRoleMenuRepository.insert(record);
            }

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

    }

    //按照父id查找menus
    @Override
    public List<CscpMenus> findByParentId(Long parentId) {
        return cscpMenusRepository.selectByParentId(parentId);
    }

    @Transactional
    @Override
    public Integer updateDrag(Map<String, CscpDragDTO> cscpDragDTOMap) {
        //目标对象
        CscpMenus target = BeanConvertUtils.copyProperties(cscpDragDTOMap.get("target"), CscpMenus.class);
        CscpMenus drag = BeanConvertUtils.copyProperties(cscpDragDTOMap.get("drag"), CscpMenus.class);

        //往上拖拽
        if (target.getOrderBy() <= drag.getOrderBy()) {
            cscpMenusRepository.updataSort(SortEnum.builder().sort(target.getOrderBy()).tableName("cscp_menus").sortName("order_by").additionOrsubtraction("+").build());

        }

        //往下拖拽
        if (target.getOrderBy() >= drag.getOrderBy()) {
            cscpMenusRepository.updataSort(SortEnum.builder().sort(target.getOrderBy()).tableName("cscp_menus").sortName("order_by").additionOrsubtraction("-").build());

        }
        drag.setOrderBy(cscpDragDTOMap.get("target").getOrderBy());

        return cscpMenusRepository.updateById(drag);
    }

    @Override
    public List<CscpMenusDTO> getDataDtOFromDomin(List<CscpMenus> list) {
        return ListCopyUtil.copy(list, CscpMenusDTO.class);
    }

    @Override
    public CscpMenusDTO copyDto(CscpMenus cscpMenus, CscpMenusDTO cscpMenusDTO) {
        cscpMenusDTO = new CscpMenusDTO();
        BeanUtils.copyProperties(cscpMenus, cscpMenusDTO);
        return cscpMenusDTO;
    }

    @Override
    public List<CscpMenusDTO> selectByMenuIdList(List<Long> menuIdList) {
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.in(CscpMenus::getId, menuIdList);
        // 如果登录的不是平台管理员帐号，需要去掉平台管理员专用的菜单
        if (SecurityUtils.isAuthenticated() && !SecurityUtils.isSystemName()) {
            queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
        }
        List<CscpMenusDTO> allMenus = ListCopyUtil.copy(cscpMenusRepository.selectList(queryWrapper), CscpMenusDTO.class);
        return allMenus;
    }

    /**
     * 根据租户ID查询其所有关联的菜单
     *
     * @param tenantId
     * @return
     */
    @Override
    public List<CscpMenusDTO> qryMenusByTenantId(Long tenantId) {
        return cscpMenusRepository.qryMenusByTenantId(tenantId);
    }

    /**
     * 获取菜单缓存
     *
     * @param userId
     * @return
     */
    @Override
    public List<CscpMenusDTO> getMenuCache(Long userId) {
        List<CscpMenusDTO> cscpMenusDTOList = new ArrayList<>();
        // 判断redis里面是否有这个key，则直接获取返回
        if (!redisUtil.hasKey(RedisKeyConstant.USER_MENU + userId)) {
            return cscpMenusDTOList;
        }
        // 获取redis里面压缩的菜单数据
        String menuStr = String.valueOf(redisUtil.get(RedisKeyConstant.USER_MENU + userId));

        // 解压菜单数据
        cscpMenusDTOList = JSONArray.parseArray(GzipUtil.uncompress(menuStr), CscpMenusDTO.class);
        return cscpMenusDTOList;
    }

    /*public static void main(String[] args) {
        String menuStr = "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";
        System.out.println(GzipUtil.uncompress(menuStr));
    }*/

    /**
     *
     * @param
     * @return
     */
    public List<SysMenusCustomization> getMenuCustomizationCache() {
        List<SysMenusCustomization> cscpMenusDTOList = new ArrayList<>();
        // 判断redis里面是否有这个key，则直接获取返回,没有将查询单位的自定义菜单保存到
        Long companyId = SecurityUtils.getCurrentCompanyId();
        if (!redisUtil.hasKey(RedisKeyConstant.COMPANY_MENU + companyId)) {
            return cscpMenusDTOList;
        }
        // 获取redis里面压缩的菜单数据
        String menuStr = String.valueOf(redisUtil.get(RedisKeyConstant.COMPANY_MENU + companyId));
        // 解压菜单数据
        cscpMenusDTOList = JSONArray.parseArray(GzipUtil.uncompress(menuStr), SysMenusCustomization.class);
        return cscpMenusDTOList;
    }

    /**
     * 获取用户uid的首页的登录信息
     *
     * @param uid
     * @return
     */
    @Override
    public List<CscpMenusDTO> getHomeCscpMenus(long uid) {
        List<CscpMenusDTO> cscpMenusDTOList = this.findByUserId(uid);
        cscpMenusDTOList = cscpMenusDTOList.stream().filter(cscpMenusDTO -> {
            return cscpMenusDTO.getHomeDisplay() != null && cscpMenusDTO.getHomeDisplay().intValue() == 1;
        }).collect(Collectors.toList());
        Long deid = SecurityUtils.getCurrentCscpUserDetail().getCompanyId();

        //根据单位ID查找修改的菜单
        SysMenusCustomizationDTO sysMenusCustomizationDTO = new SysMenusCustomizationDTO();
        sysMenusCustomizationDTO.setCompanyId(deid);
        LambdaQueryWrapper<SysMenusCustomization> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysMenusCustomization :: getDepartmentId,deid);
        List<SysMenusCustomization> listData = sysMenusCustomizationMapper.selectList(queryWrapper);

        //取不同
//        List<CscpMenusDTO> finalCscpMenusDTOList = cscpMenusDTOList;
//        List<SysMenusCustomization> reduce = listData.stream().filter(
//                a -> !finalCscpMenusDTOList.stream().map(
//                        b -> b.getId()).collect(Collectors.toList())
//                        .contains(a.getMenuId())).collect(Collectors.toList());

        //过滤一次是否有相同的
        if(!listData.isEmpty()){
            cscpMenusDTOList.forEach(a ->  listData.forEach( b -> {
                if(a.getId().equals(b.getMenuId())){
                    a.setHomeArea(b.getHomeArea());
                    a.setHomeDisplay(b.getHomeDisplay());
                }
            }));
        }
//        for(SysMenusCustomization ent : reduce){
//            CscpMenusDTO menudto = new CscpMenusDTO();
//            BeanUtils.copyProperties(ent,menudto);
//            menudto.setId(ent.getMenuId());
//            cscpMenusDTOList.add(menudto);
//        }

        return cscpMenusDTOList;
    }

    /**
     * 通过角色ID查询其关联的菜单信息
     *
     * @param roleId
     * @return
     */
    @Override
    public List<CscpMenusDTO> getMenuListByRoleId(Long roleId) {
        LambdaQueryWrapper<CscpRoleMenu> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpRoleMenu::getRoleId, roleId);
        List<Long> menuIdList = cscpRoleMenuRepository.selectList(queryWrapper).stream()
                .map(x -> x.getMenuId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(menuIdList)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<CscpMenus> lambdaQueryWrapper = new LambdaQueryWrapper();
        lambdaQueryWrapper.in(CscpMenus::getId, menuIdList);
        // 如果登录的不是平台管理员帐号，需要去掉平台管理员专用的菜单
        if (SecurityUtils.isAuthenticated() && !SecurityUtils.isSystemName()) {
            lambdaQueryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
        }
        List<CscpMenus> menusList = cscpMenusRepository.selectList(lambdaQueryWrapper);
        return ListCopyUtil.copy(menusList, CscpMenusDTO.class);
    }

    /**
     * 组装菜单树
     *
     * @param allMenuList
     * @param checkedMenuIdSet
     * @return
     */
    @Override
    public List<Tree<String>> assembledTreeNodeList(List<CscpMenusDTO> allMenuList, Set<Long> checkedMenuIdSet) {
        // 获取数据规则列表的menuIdSet
        LambdaQueryWrapper<CscpDataRule> cdrl = new LambdaQueryWrapper<>();
        Set<Long> ruleMenuIdSet = cscpDataRuleMapper.selectList(cdrl).stream()
                .map(x -> x.getMenuId()).collect(Collectors.toSet());

        // 获取非叶节点的菜单ID
        Set<Long> parentMenuIdSet = allMenuList.stream().map(x -> x.getParentId()).collect(Collectors.toSet());

        // 构建的整个树数据
        List<TreeNode<String>> treeNodeList = allMenuList.stream().map(menu -> {
            Map<String, Object> extraMap = new HashMap<>();
            if (menu.getUrl() == null) {
                extraMap.put("href", "#");
            } else {
                extraMap.put("sref", menu.getUrl());
            }
            extraMap.put("icon", menu.getIcon());
            // 只有叶节点菜单才设置为true
            if (checkedMenuIdSet.contains(menu.getId()) && !parentMenuIdSet.contains(menu.getId())) {
                extraMap.put("checked", true);
            } else {
                extraMap.put("checked", false);
            }
            if (ruleMenuIdSet.contains(menu.getId())) {
                extraMap.put("permission", true);
            }
            extraMap.put("hasPermission", false);

            // 单个树数据构建
            TreeNode<String> treeNode = new TreeNode<String>()
                    .setId(String.valueOf(menu.getId()))
                    .setParentId(String.valueOf(menu.getParentId()))
                    .setName(menu.getTitle())
                    .setWeight(menu.getOrderBy())
                    .setExtra(extraMap);
            return treeNode;
        }).collect(Collectors.toList());

        // 配置
        TreeNodeConfig treeNodeConfig = new TreeNodeConfig();
        // 自定义属性名 都要默认值的
        treeNodeConfig.setNameKey("title");
        treeNodeConfig.setChildrenKey("items");
        // 最大递归深度
        treeNodeConfig.setDeep(6);

        //转换器
        List<Tree<String>> treeNodes = TreeUtil.build(treeNodeList, "0", treeNodeConfig,
                (treeNode, tree) -> {
                    tree.setId(treeNode.getId());
                    tree.setParentId(treeNode.getParentId());
                    tree.setWeight(treeNode.getWeight());
                    tree.setName(treeNode.getName());
                    // 扩展属性值赋值
                    // treeNode.getExtra().getOrDefault("domain", null) 是获取上面Map放进去的值，没有就是 null
                    tree.putExtra("href", treeNode.getExtra().getOrDefault("href", null));
                    tree.putExtra("sref", treeNode.getExtra().getOrDefault("sref", null));
                    tree.putExtra("icon", treeNode.getExtra().getOrDefault("icon", null));
                    tree.putExtra("checked", treeNode.getExtra().getOrDefault("checked", false));
                    tree.putExtra("permission", treeNode.getExtra().getOrDefault("permission", false));
                    tree.putExtra("hasPermission", treeNode.getExtra().getOrDefault("hasPermission", false));
                });
        return treeNodes;
    }

    /**
     * 修改菜单信息
     *
     * @param cscpMenusDtos
     * @return
     */
    @Override
    public Integer updateOrderBy(CscpMenusDTO cscpMenusDtos) {
        //处理排序
        cscpMenusRepository.updataSort(
                SortEnum.builder()
                        .sort(cscpMenusDtos.getOrderBy())
                        .parentId(cscpMenusDtos.getParentId())
                        .id(cscpMenusDtos.getId())
                        .tableName("cscp_menus")
                        .sortName("order_by")
                        .additionOrsubtraction("+")
                        .build());

        CscpMenus cscpMenus = BeanConvertUtils.copyProperties(cscpMenusDtos, CscpMenus.class);
        int i = cscpMenusRepository.updateById(cscpMenus);

        return i;
    }

    /**
     * 判断是否还有子菜单
     *
     * @param menuId
     * @return
     */
    private Boolean hasChildMenu(Long menuId) {
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(CscpMenus::getParentId, menuId);
        Integer count = cscpMenusRepository.selectCount(queryWrapper);
        return count > 0;
    }


    /**
     * 拖拽菜单，admin用，(不跨父母节点)
     *
     * @param target
     * @param drag
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CscpMenus> updateDrag(CscpMenus target, CscpMenus drag) {
        List<CscpMenus> cscpMenusList = new ArrayList<>();
        List<Integer> integerList = new ArrayList<>();
        LambdaQueryWrapper<CscpMenus> cscpMenusLambdaQueryWrapper = Wrappers.lambdaQuery();
        cscpMenusLambdaQueryWrapper.eq(CscpMenus::getParentId, target.getParentId());
        cscpMenusLambdaQueryWrapper.orderByAsc(CscpMenus::getOrderBy);
        //往上拖拽
        if (target.getOrderBy() >= drag.getOrderBy()) {
            for (int i = drag.getOrderBy(); i < target.getOrderBy(); i++) {
                integerList.add(i);
            }
            //先传入拖拽目标，后添加被影响菜单
            cscpMenusList.add(target);
            cscpMenusLambdaQueryWrapper.in(CscpMenus::getOrderBy, integerList);
            cscpMenusList.addAll(cscpMenusRepository.selectList(cscpMenusLambdaQueryWrapper));
            //改变排序值
            for (int i = 0; i < cscpMenusList.size(); i++) {
                cscpMenusList.get(i).setOrderBy(i + drag.getOrderBy());
            }
        } else {
            for (int i = target.getOrderBy() + 1; i <= drag.getOrderBy(); i++) {
                integerList.add(i);
            }
            //先添加被影响菜单，后传入拖拽目标
            cscpMenusLambdaQueryWrapper.in(CscpMenus::getOrderBy, integerList);
            cscpMenusList.addAll(cscpMenusRepository.selectList(cscpMenusLambdaQueryWrapper));
            cscpMenusList.add(target);
            //改变排序值
            for (int i = 0; i < cscpMenusList.size(); i++) {
                cscpMenusList.get(i).setOrderBy(i + target.getOrderBy());
            }
        }
        cscpMenusList.forEach(i -> cscpMenusRepository.updateById(i));
        return cscpMenusList;
    }

    /**
     * 拖拽菜单，(跨父母节点)
     *
     * @param target
     * @param drag
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<CscpMenus> updateParentId(CscpMenus target, CscpMenus drag) {
        target.setParentId(drag.getParentId());
        target.setOrderBy(drag.getOrderBy());
        cscpMenusRepository.updataSort(SortEnum.builder()
                .sort(target.getOrderBy())
                .parentId(drag.getParentId())
                .tableName("cscp_menus")
                .sortName("order_by")
                .additionOrsubtraction("+")
                .build());
        cscpMenusRepository.updateById(target);
        LambdaQueryWrapper<CscpMenus> cscpMenusLambdaQueryWrapper = Wrappers.lambdaQuery();
        cscpMenusLambdaQueryWrapper.ge(CscpMenus::getOrderBy, target.getOrderBy());
        cscpMenusLambdaQueryWrapper.eq(CscpMenus::getParentId, target.getParentId());
        List<CscpMenus> cscpMenusList = cscpMenusRepository.selectList(cscpMenusLambdaQueryWrapper);
        return cscpMenusList;
    }

    @Override
    public void dragType(CscpMenus target, CscpMenus drag) {
        if (target.getParentId() == drag.getParentId()) {
            this.updateDrag(target, drag);
        } else {
            this.updateParentId(target, drag);
        }
    }


    /**
     * 组装单位自定义菜单集合
     *
     * @param menuList
     * @return
     */
    @Override
    public List<CscpMenusDTO> assembledCustomizeList(List<CscpMenusDTO> menuList) {
        // 如果用户未登录或者非单位管理员，则无需组装，直接拿默认菜单即可
        if (!SecurityUtils.isAuthenticated() || !SecurityUtils.isGeneralName()) {
            return menuList;
        }
        Long companyId = SecurityUtils.getCurrentCompanyId();
        if (Objects.isNull(companyId)) {
            return menuList;
        }
        LambdaQueryWrapper<SysMenusCustomization> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SysMenusCustomization::getCompanyId, companyId);
        Map<Long, List<SysMenusCustomization>> sysMenusCustomizationMap = sysMenusCustomizationService.selectListNoAdd(queryWrapper)
                .stream().collect(Collectors.groupingBy(SysMenusCustomization::getMenuId));

        // 原菜单表数据按菜单ID分组
        Map<Long, List<CscpMenusDTO>> menuListMap = menuList.stream().collect(Collectors.groupingBy(CscpMenusDTO::getId));

        for (CscpMenusDTO menusDTO : menuList) {
            CscpMenusDTO cscpMenusDTO = this.getParentType(menusDTO, menuListMap, sysMenusCustomizationMap);
            menusDTO.setParentType(cscpMenusDTO.getParentType());
            List<SysMenusCustomization> sysMenusCustomizations = sysMenusCustomizationMap.get(menusDTO.getId());
            if (CollectionUtils.isEmpty(sysMenusCustomizations)) {
                continue;
            }
            SysMenusCustomization sysMenusCustomization = sysMenusCustomizations.get(0);
            BeanUtils.copyProperties(sysMenusCustomization, menusDTO);
            menusDTO.setId(sysMenusCustomization.getMenuId());
        }

        // 按排序号排序
        List<CscpMenusDTO> collect = menuList.stream().sorted(
                Comparator.comparing(CscpMenusDTO::getOrderBy, Comparator.naturalOrder())
        ).collect(Collectors.toList());
        return collect;
    }


    /**
     * 查询除按钮外的所有菜单
     *
     * @return
     */
    @Override
    public List<CscpMenusDTO> findAllMenusExceptButton(List<Long> idList) {
        LambdaQueryWrapper<CscpMenus> queryWrapper = new LambdaQueryWrapper();
        // 如果登录的不是平台管理员帐号，需要去掉平台管理员专用的菜单
        if (SecurityUtils.isAuthenticated() && !SecurityUtils.isSystemName()) {
            queryWrapper.ne(CscpMenus::getPlatformAdminUse, CscpMenusDTO.ONLY_ADMIN_USE);
        }
        if (CollectionUtils.isNotEmpty(idList)) {
            queryWrapper.in(CscpMenus::getId, idList);
        }
//        queryWrapper.ne(CscpMenus::getType, MENUS_BUTTON_TYPE);
        List<CscpMenus> menusList = cscpMenusRepository.selectList(queryWrapper);
        return ListCopyUtil.copy(menusList, CscpMenusDTO.class);
    }

    /**
     * 获取父级节点类型
     *
     * @param menusDTO
     * @param menuListMap
     * @param sysMenusCustomizationMap
     * @return
     */
    private CscpMenusDTO getParentType(CscpMenusDTO menusDTO, Map<Long, List<CscpMenusDTO>> menuListMap, Map<Long, List<SysMenusCustomization>> sysMenusCustomizationMap) {

        // 查询该菜单是否变更过
        List<SysMenusCustomization> sysMenusCustomizations = sysMenusCustomizationMap.get(menusDTO.getId());

        // 如果为空，则该菜单表明没有挪动过，直接拿父节点ID
        if (CollectionUtils.isEmpty(sysMenusCustomizations)) {
            // 如果父级节点为顶级节点，则直接返回数据
            if (ROOT_MENU_ID.equals(menusDTO.getParentId())) {
                return menusDTO;
            }
            // 如果父级节点不是顶级，则需要判断其父级节点是否挪动
            List<SysMenusCustomization> parentMenusCustomizations = sysMenusCustomizationMap.get(menusDTO.getParentId());

            // 如果为空，则表明其父节点没有挪动过
            if (CollectionUtils.isEmpty(parentMenusCustomizations)) {
                if (CollectionUtils.isNotEmpty(menuListMap.get(menusDTO.getParentId()))) {
                    menusDTO.setParentType(menuListMap.get(menusDTO.getParentId()).get(0).getType());
                }
                return menusDTO;
            }

            // 如果不为空，则表明器父节点挪动过
            menusDTO.setParentType(parentMenusCustomizations.get(0).getType());
            return menusDTO;
        }

        // 如果不为空，则表明该菜单有挪动过，获取其挪动后父节点信息
        SysMenusCustomization sysMenusCustomization = sysMenusCustomizations.get(0);

        // 如果其父级节点为根节点，则直接返回数据
        if (ROOT_MENU_ID.equals(sysMenusCustomization.getParentId())) {
            return menusDTO;
        }

        // 如果父节点不是根节点，需要查询其父节点是否挪动
        List<SysMenusCustomization> parentMenusCustomizations = sysMenusCustomizationMap.get(sysMenusCustomization.getParentId());

        // 如果父节点没有在自定义表中找到则需要去原菜单表找
        if (CollectionUtils.isEmpty(parentMenusCustomizations)) {
            List<CscpMenusDTO> cscpMenusDTOS = menuListMap.get(sysMenusCustomization.getParentId());
            if (CollectionUtils.isNotEmpty(cscpMenusDTOS)) {
                menusDTO.setParentType(cscpMenusDTOS.get(0).getType());
            }
            return menusDTO;

        }
        // 如果在自定义表中找到了该菜单的父级节点，则直接赋值
        menusDTO.setParentType(parentMenusCustomizations.get(0).getType());
        return menusDTO;
    }
}

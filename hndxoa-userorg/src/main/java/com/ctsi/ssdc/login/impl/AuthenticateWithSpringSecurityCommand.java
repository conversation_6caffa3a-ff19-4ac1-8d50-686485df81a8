package com.ctsi.ssdc.login.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.IpUtil;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.entity.CscpUserLockRecord;
import com.ctsi.ssdc.admin.repository.CscpUserLockRecordMapper;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.login.LoginCommand;
import com.ctsi.ssdc.login.LoginContext;
import com.ctsi.ssdc.login.LoginParent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.AuthenticationException;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * <AUTHOR> 15433
 * @date : 2025/05/19/9:42
 * description:
 */
@Component
@Order(10005)
public class AuthenticateWithSpringSecurityCommand extends LoginParent implements LoginCommand {

    private final Logger logger = LoggerFactory.getLogger(DecryptPasswordCommand.class);

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private CscpUserLockRecordMapper userLockRecordMapper;

    @Autowired
    CscpUserService cscpUserService;

    @Override
    public void execute(LoginContext context) {
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                context.cscpUser.getLoginName(), context.finalPassword);

        try {
            context.authentication = authenticationManager.authenticate(authenticationToken);
        } catch (AuthenticationException e) {
            handleAuthException(context, e);
        }
    }

    private void handleAuthException(LoginContext context, AuthenticationException e) {
        if (!(e instanceof InternalAuthenticationServiceException)) {
            if (context.loginCache != null) {
                Long count = context.redisUtil.increment(context.countKey, 1);
                if (count != null && count > context.badPasswordAttempts) {
                    context.loginCache.put(context.countKey, context.badPasswordAttempts + 1);
                    cscpUserService.update(Wrappers.<CscpUser>lambdaUpdate()
                            .set(CscpUser::getStatus, 0)
                            .eq(CscpUser::getId, context.cscpUser.getId()));

                    CscpUserLockRecord record = new CscpUserLockRecord();
                    record.setUserId(context.cscpUser.getId());
                    record.setLockTime(LocalDateTime.now());
                    record.setLockIp(IpUtil.getRealIp(context.request));
                    userLockRecordMapper.insert(record);

                    context.systemLogOperation.setObjectBody("密码输入错误达到上限锁定");
                    context.systemLogOperation.setOperationType(2);
                    context.systemLogOperation.setOperationResult("失败");
                    logAsync(context.systemLogOperation);

                    context.redisUtil.deleteByPattern("Bearer:pc*" + context.cscpUser.getId());
                    context.redisUtil.sSet("Bearer:pc:lock", context.cscpUser.getId());

                    throw new BusinessException("密码输入错误次数达到{}次，请{}分钟之后重试",
                            context.badPasswordAttempts, context.lockoutTime / 60);
                } else {
                    // 剩余可尝试次数
                    throw new BusinessException("用户名或密码错误，还有 {} 次尝试机会", context.badPasswordAttempts - count.intValue());
                }
            }
            logger.error("-----登录认证失败:", e);
        } else {
            logger.error("-----登录认证失败:", e);
            context.systemLogOperation.setObjectBody("登录异常，请确认用户密码是否正确");
            context.systemLogOperation.setOperationType(2);
            context.systemLogOperation.setOperationResult("失败");
            logAsync(context.systemLogOperation);
            throw new BusinessException("登录异常，请确认用户密码是否正确，" + e);
        }
    }
}

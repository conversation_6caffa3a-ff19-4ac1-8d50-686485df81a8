package com.ctsi.ssdc.login;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> 15433
 * @date : 2025/05/19/10:20
 * description:
 */
@Service
public class LoginEngine {

    @Autowired
    private List<LoginCommand> commands;

    public void run(LoginContext context) {
        for (LoginCommand command : commands) {
            command.execute(context);
        }
    }
}
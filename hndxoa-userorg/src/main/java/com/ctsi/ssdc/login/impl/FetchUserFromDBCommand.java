package com.ctsi.ssdc.login.impl;

import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.login.LoginCommand;
import com.ctsi.ssdc.login.LoginContext;
import com.ctsi.ssdc.login.RequestUserContextUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 15433
 * @date : 2025/05/19/9:39
 * description:
 */
@Component
@Order(10002)
public class FetchUserFromDBCommand implements LoginCommand {

    @Autowired
    private CscpUserService cscpUserService;

    @Override
    public void execute(LoginContext context) {
        context.cscpUser = cscpUserService.getUserByUsernameOrMobile(context.finalUserName);
        RequestUserContextUtil.setLoginContext(context.cscpUser);
        if (context.cscpUser == null) {
            throw new BusinessException(ResultCode.USER_LOGIN_ERROR);
        }
    }
}

package com.ctsi.personMatters.service;

import com.ctsi.personMatters.entity.dto.BizPersonDossierImportRecordDTO;
import com.ctsi.personMatters.entity.BizPersonDossierImportRecord;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 干部人员导入记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-16
 */
public interface IBizPersonDossierImportRecordService extends SysBaseServiceI<BizPersonDossierImportRecord> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<BizPersonDossierImportRecordDTO> queryListPage(BizPersonDossierImportRecordDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<BizPersonDossierImportRecordDTO> queryList(BizPersonDossierImportRecordDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    BizPersonDossierImportRecordDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    BizPersonDossierImportRecordDTO create(BizPersonDossierImportRecordDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(BizPersonDossierImportRecordDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByBizPersonDossierImportRecordId
     * @param code
     * @return
     */
    boolean existByBizPersonDossierImportRecordId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<BizPersonDossierImportRecordDTO> dataList);


}

package com.ctsi.personMatters.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndx.tree.Node;
import com.ctsi.personMatters.entity.dto.BizDossierOrganizationDTO;
import com.ctsi.personMatters.entity.dto.BizPersonDossierDTO;
import com.ctsi.personMatters.service.IBizDossierOrganizationService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import com.ctsi.ssdc.security.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-17
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/bizDossierOrganization")
@Api(value = "内设机构管理表", tags = "内设机构管理表接口")
public class BizDossierOrganizationController extends BaseController {

    private static final String ENTITY_NAME = "bizDossierOrganization";

    @Autowired
    private IBizDossierOrganizationService bizDossierOrganizationService;



    /**
     *  新增内设机构管理表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.bizDossierOrganization.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增内设机构管理表批量数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizDossierOrganization.add')")
    public ResultVO createBatch(@RequestBody List<BizDossierOrganizationDTO> bizDossierOrganizationList) {
        Boolean  result = bizDossierOrganizationService.insertBatch(bizDossierOrganizationList);
        if(result){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.bizDossierOrganization.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增内设机构管理表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizDossierOrganization.add')")
    public ResultVO<BizDossierOrganizationDTO> create(@RequestBody BizDossierOrganizationDTO bizDossierOrganizationDTO)  {
        BizDossierOrganizationDTO result = bizDossierOrganizationService.create(bizDossierOrganizationDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.bizDossierOrganization.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新内设机构管理表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizDossierOrganization.update')")
    public ResultVO update(@RequestBody BizDossierOrganizationDTO bizDossierOrganizationDTO) {
        Assert.notNull(bizDossierOrganizationDTO.getId(), "general.IdNotNull");
        int count = bizDossierOrganizationService.update(bizDossierOrganizationDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除内设机构管理表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.bizDossierOrganization.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.bizDossierOrganization.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = bizDossierOrganizationService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        BizDossierOrganizationDTO bizDossierOrganizationDTO = bizDossierOrganizationService.findOne(id);
        return ResultVO.success(bizDossierOrganizationDTO);
    }

    /**
     *  分页查询多条数据.
     */
    @GetMapping("/queryBizDossierOrganizationPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizDossierOrganizationDTO>> queryBizDossierOrganizationPage(BizDossierOrganizationDTO bizDossierOrganizationDTO, BasePageForm basePageForm) {
        return ResultVO.success(bizDossierOrganizationService.queryListPage(bizDossierOrganizationDTO, basePageForm));
    }

    /**
     *  查询机构下的所有干部信息.
     */
    @GetMapping("/findDossierListPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<BizPersonDossierDTO>> findDossierListPage(BizPersonDossierDTO query, BasePageForm basePageForm) {
        return ResultVO.success(bizDossierOrganizationService.findDossierListPage(query, basePageForm));
    }

    @ApiOperation(value = "查询所有机构信息,不需要勾选已经选择的节点")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "parentId", value = "当前节点的父节点", defaultValue = "0", required = true),
    })
    @GetMapping("/selectDossierOrgList")
    public ResultVO<List<Node<BizDossierOrganizationDTO>>> selectDossierOrgList(@RequestParam(value = "parentId", required = true, defaultValue = "0")
                                                                     Long parentId) {
        Node<BizDossierOrganizationDTO> parentNode = new Node<>();
        List<Node<BizDossierOrganizationDTO>> orgTreeDataNode = bizDossierOrganizationService.selectChildrenListNodeByParentId(parentId);
       /* if (SecurityUtils.isGeneralName()) {
            parentNode.setChildren(orgTreeDataNode);
            List<Node<BizDossierOrganizationDTO>> result = new ArrayList<>();
            result.add(parentNode);
            return ResultVO.success(result);
        } else {*/
            return ResultVO.success(orgTreeDataNode);
       // }

    }


    /**
     * 查询多条数据.不分页
     */
    @GetMapping("/queryBizDossierOrganization")
    @ApiOperation(value = "查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<ResResult<BizDossierOrganizationDTO>> queryBizDossierOrganization(BizDossierOrganizationDTO bizDossierOrganizationDTO) {
        List<BizDossierOrganizationDTO> list = bizDossierOrganizationService.queryList(bizDossierOrganizationDTO);
        return ResultVO.success(new ResResult<BizDossierOrganizationDTO>(list));
    }

}

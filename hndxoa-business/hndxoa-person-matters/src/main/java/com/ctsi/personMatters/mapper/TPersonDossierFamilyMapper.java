package com.ctsi.personMatters.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.ctsi.personMatters.entity.TPersonDossierFamily;
import com.ctsi.hndx.common.MybatisBaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

/**
 * <p>
 * 人事档案表附表——家属 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface TPersonDossierFamilyMapper extends MybatisBaseMapper<TPersonDossierFamily> {


    @Update({"update t_person_dossier_family set deleted =1 where dossier_id = #{dossier_id}"})
    @InterceptorIgnore(tenantLine = "true")
    void deleteDossierFamilyByUserId(@Param("dossier_id") Long dossier_id);

}

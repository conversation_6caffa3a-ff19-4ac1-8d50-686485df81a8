package com.ctsi.personMatters.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.personMatters.entity.TPersonDossierFamily;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.personMatters.entity.dto.TPersonDossierDTO;
import com.ctsi.personMatters.entity.dto.TPersonDossierFamilyDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

/**
 * <p>
 * 人事档案表附表——家属 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-15
 */
public interface ITPersonDossierFamilyService extends SysBaseServiceI<TPersonDossierFamily> {

    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TPersonDossierFamilyDTO> queryListPage(TPersonDossierFamilyDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TPersonDossierFamilyDTO> queryList(TPersonDossierFamilyDTO entity);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TPersonDossierFamilyDTO findOne(Long id);

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    TPersonDossierFamilyDTO create(TPersonDossierFamilyDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TPersonDossierFamilyDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

    /**
     * 批量新增
     *
     * create batch
     * @param dataList
     * @return
     */
    Boolean insertBatch(List<TPersonDossierFamilyDTO> dataList);


}

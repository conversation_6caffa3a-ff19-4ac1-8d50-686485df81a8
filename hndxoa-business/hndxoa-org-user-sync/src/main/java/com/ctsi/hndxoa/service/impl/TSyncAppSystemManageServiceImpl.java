package com.ctsi.hndxoa.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.addrbook.entity.TAddressBook;
import com.ctsi.hndx.addrbook.service.ITAddressBookService;
import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.constant.SysConfigConstant;
import com.ctsi.hndx.enums.SystemRole;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.*;
import com.ctsi.hndx.westoneuas.westone.WestoneUasApiUtil;
import com.ctsi.hndx.wps.WpsUtil;
import com.ctsi.hndxoa.entity.*;
import com.ctsi.hndxoa.entity.dto.*;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.hndxoa.mapper.TSyncOrgHistroyRecordMapper;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.mq.consumer.HttpPushUserConsumer;
import com.ctsi.hndxoa.mq.consumer.SyncCommonHttpConsumer;
import com.ctsi.hndxoa.mq.producer.HttpPushOrgProducer;
import com.ctsi.hndxoa.mq.producer.HttpPushUserProducer;
import com.ctsi.hndxoa.mq.producer.OrgProducer;
import com.ctsi.hndxoa.mq.producer.UserProducer;
import com.ctsi.hndxoa.pull.entity.dto.PassiveSyncMessage;
import com.ctsi.hndxoa.pull.service.ITSyncAppSystemManagePullService;
import com.ctsi.hndxoa.service.ITSyncAppModeratorManageService;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.ITSyncOrgHistroyRecordService;
import com.ctsi.hndxoa.service.IWestoneUasManageService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import com.ctsi.hndxoa.service.constant.SyncAppSystemEnum;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpRoles;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRoleRepository;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.conn.HttpHostConnectException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 同步应用系统管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
@Slf4j
@Service
public class TSyncAppSystemManageServiceImpl extends SysBaseServiceImpl<TSyncAppSystemManageMapper, TSyncAppSystemManage> implements ITSyncAppSystemManageService {

    @Autowired
    private TSyncAppSystemManageMapper tSyncAppSystemManageMapper;

    @Autowired
    private CscpOrgService cscpOrgService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;
    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private TSyncOrgHistroyRecordMapper tSyncOrgHistroyRecordMapper;

    @Autowired
    private ITSyncOrgHistroyRecordService itSyncOrgHistroyRecordService;

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private TSyncUserHistroyRecordMapper tSyncUserHistroyRecordMapper;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private ITAddressBookService tAddressBookService;

    @Autowired
    private IWestoneUasManageService westoneUasManageService;

    @Autowired
    private UserProducer userProducer;

    @Autowired
    private OrgProducer orgProducer;

    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private CscpUserRoleRepository cscpUserRoleRepository;

    @Autowired
    private HttpPushUserProducer httpPushUserProducer;

    @Autowired
    private HttpPushOrgProducer httpPushOrgProducer;

    @Autowired
    private ITSyncAppModeratorManageService itSyncAppModeratorManageService;

    @Autowired
    private ITSyncAppSystemManagePullService itSyncAppSystemManagePullService;

    @Autowired
    private ApplicationEventPublisher eventPublisher;

    private static final ExecutorService threadPool = Executors.newFixedThreadPool(50);

    /**
     * 手动推送类型
     */
    public static final String MANUAL_PUSH = "0";

    /**
     * 自动推送类型
     */
    public static final String AUTO_PUSH = "1";

    /**
     * 程序结束做关闭
     */
    @PreDestroy
    public void shutdownThreadPool() {
        if (!threadPool.isShutdown()) {
            threadPool.shutdown();
            try {
                if (!threadPool.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.warn("线程池threadPool未能在指定时间内完成所有任务，强制关闭");
                    threadPool.shutdownNow();
                }
            } catch (InterruptedException e) {
                log.error("线程池threadPool关闭过程中发生中断，原因: {}", e.getMessage(), e);
                threadPool.shutdownNow();
            }
        }
    }

    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSyncAppSystemManageDTO> queryListPage(TSyncAppSystemManageDTO entityDTO, BasePageForm basePageForm) {

        // 单位管理员
        //if (SecurityUtils.isUnitAdmin()) {
        //    return unitAdminQueryAppPage(entityDTO, basePageForm);
        //}

        // 设置分页
        IPage<TSyncAppSystemManage> page = PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getAppName()), TSyncAppSystemManage::getAppName, entityDTO.getAppName());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getAppCode()), TSyncAppSystemManage::getAppCode, entityDTO.getAppCode());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getSyncUrl()), TSyncAppSystemManage::getSyncUrl, entityDTO.getSyncUrl());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getStatus()), TSyncAppSystemManage::getStatus, entityDTO.getStatus());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getAuthentication()), TSyncAppSystemManage::getAuthentication, entityDTO.getAuthentication());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getInSystemFlag()), TSyncAppSystemManage::getInSystemFlag, entityDTO.getInSystemFlag());
        queryWrapper.eq(ObjectUtil.isNotNull(entityDTO.getRequestMode()), TSyncAppSystemManage::getRequestMode, entityDTO.getRequestMode());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getAppId()), TSyncAppSystemManage::getAppId, entityDTO.getAppId());
        queryWrapper.like(StringUtils.isNotEmpty(entityDTO.getAppKey()), TSyncAppSystemManage::getAppKey, entityDTO.getAppKey());
        // 筛选版主应用
        queryWrapper.isNotNull(ObjectUtil.equal(entityDTO.getModeratorFlag(),1), TSyncAppSystemManage::getRoleId);
        queryWrapper.orderByDesc(TSyncAppSystemManage::getCreateTime);

        // 如果是admin用户，保留原有逻辑查询全部
        Long userId = SecurityUtils.getCurrentUserId();
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapperRole = new LambdaQueryWrapper<>();
        queryWrapperRole.select(TSyncAppSystemManage::getRoleId)
                .isNotNull(TSyncAppSystemManage::getRoleId);
        List<TSyncAppSystemManage> appSystemManages = tSyncAppSystemManageMapper.selectListNoAdd(queryWrapperRole);
        if(CollUtil.isNotEmpty(appSystemManages)){
            List<Long>  roleIds = appSystemManages.stream().map(TSyncAppSystemManage::getRoleId).collect(Collectors.toList());
            // 查询当前用户所属的角色ID列表
            List<Long> userRoleIds = tSyncAppSystemManageMapper.selectUserRoleIds(userId, roleIds);
            if (CollUtil.isNotEmpty(userRoleIds)) {
                //区划管理要看所有的应用
                if (SecurityUtils.isRegionAdmin()) {
                    queryWrapper.and(wrapper -> {
                        wrapper.in(TSyncAppSystemManage::getRoleId, userRoleIds);
                        // 只有在特不筛选版主应用时，也需要查询所有通用应用
                        if (ObjectUtil.notEqual(entityDTO.getModeratorFlag(),1)) {
                            wrapper.or().isNull(TSyncAppSystemManage::getRoleId);
                        }
                    });
                }else if (SecurityUtils.isUnitAdmin()) {
                    // 单位管理员看被授权的应用
                    queryWrapper.in(TSyncAppSystemManage::getId, cscpUserRepository.getModeratorManageAppIds(SecurityUtils.getCurrentUserId()));
                }else {
                    // 普通用户只看自己有权限的应用
                    queryWrapper.in(TSyncAppSystemManage::getRoleId, userRoleIds);
                }
            } else {
                queryWrapper.and(qw -> qw.isNull(TSyncAppSystemManage::getRoleId)
                        .or().notIn(TSyncAppSystemManage::getRoleId, roleIds));
            }
        }
        page = tSyncAppSystemManageMapper.selectPageNoAdd(page, queryWrapper);

        //返回
        IPage<TSyncAppSystemManageDTO> data = page.convert(entity -> BeanConvertUtils.copyProperties(entity, TSyncAppSystemManageDTO.class));
        // 批量设置以授权机构信息
        this.setOrgMapForRecords(data.getRecords());

        data.getRecords().forEach(x -> {
            // TODO 接口服务地址、appId和appKey脱敏展示
            if (StringUtils.isNotEmpty(x.getSyncUrl())) {
                x.setSyncUrl(DesensitizeUtil.desensitizedUrl(x.getSyncUrl()));
            }
            if (StringUtils.isNotEmpty(x.getInSystemUrl())) {
                x.setInSystemUrl(DesensitizeUtil.desensitizedUrl(x.getInSystemUrl()));
            }
            if (StringUtils.isNotEmpty(x.getAppId())) {
                x.setAppId("******");
            }
            if (StringUtils.isNotEmpty(x.getAppKey())) {
                x.setAppKey("******");
            }
        });
        return new PageResult<TSyncAppSystemManageDTO>(data.getRecords(),
                data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSyncAppSystemManageDTO> queryList(TSyncAppSystemManageDTO entityDTO) {
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapper = new LambdaQueryWrapper();
        List<TSyncAppSystemManage> listData = tSyncAppSystemManageMapper.selectList(queryWrapper);
        List<TSyncAppSystemManageDTO> TSyncAppSystemManageDTOList = ListCopyUtil.copy(listData, TSyncAppSystemManageDTO.class);
        return TSyncAppSystemManageDTOList;
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSyncAppSystemManageDTO findOne(Long id) {
        TSyncAppSystemManage tSyncAppSystemManage = tSyncAppSystemManageMapper.selectById(id);
        return BeanConvertUtils.copyProperties(tSyncAppSystemManage, TSyncAppSystemManageDTO.class);
    }


    /**
     * 新增
     *
     * @param entityDTO the entity to create
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TSyncAppSystemManageDTO create(TSyncAppSystemManageDTO entityDTO) {
        TSyncAppSystemManage tSyncAppSystemManage = BeanConvertUtils.copyProperties(entityDTO, TSyncAppSystemManage.class);
        if (tSyncAppSystemManage != null) {
            save(tSyncAppSystemManage);
        }
        return BeanConvertUtils.copyProperties(tSyncAppSystemManage, TSyncAppSystemManageDTO.class);
    }

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSyncAppSystemManageDTO entity) {
        TSyncAppSystemManage tSyncAppSystemManage = BeanConvertUtils.copyProperties(entity, TSyncAppSystemManage.class);
        if (tSyncAppSystemManage != null) {
            if (StringUtils.isEmpty(tSyncAppSystemManage.getInOwnedOrgId())) {
                tSyncAppSystemManage.setInOwnedOrgId(null);
            }
            if (tSyncAppSystemManage.getRoleId() == null) {
                // 如果应用角色ID为空，则删除应用绑定的角色信息
                tSyncAppSystemManage.setRoleName(null);
                tSyncAppSystemManage.setModeratorFlag(0);
            }else{
                tSyncAppSystemManage.setModeratorFlag(1);
            }
        }
        // 获取更新前的角色信息
        TSyncAppSystemManage currentApp = tSyncAppSystemManageMapper.selectById(entity.getId());
        Long oldRoleId = currentApp.getRoleId();
        Long newRoleId = tSyncAppSystemManage.getRoleId();

        int updateStatus = tSyncAppSystemManageMapper.updateById(tSyncAppSystemManage);

        // 当角色ID发生变化时，同步版主信息
        if (!Objects.equals(oldRoleId, newRoleId)) {
            log.info("应用(appId:{})的角色发生变化，开始同步版主信息。旧角色ID:{}, 新角色ID:{}", entity.getId(), oldRoleId, newRoleId);
            syncModeratorsForApp(entity.getId(), newRoleId);
        }

        if (WestoneUasManageService.WESTONE_UAS_SYSTEM.equals(entity.getAppCode()) && updateStatus > 0) {
            try {
                WestoneUasApiUtil.resetInstance();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return updateStatus;
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tSyncAppSystemManageMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSyncAppSystemManageId
     * @return
     */
    @Override
    public boolean existByTSyncAppSystemManageId(Long TSyncAppSystemManageId) {
        if (TSyncAppSystemManageId != null) {
            LambdaQueryWrapper<TSyncAppSystemManage> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSyncAppSystemManage::getId, TSyncAppSystemManageId);
            List<TSyncAppSystemManage> result = tSyncAppSystemManageMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
     * 批量新增
     *
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TSyncAppSystemManageDTO> dataList) {
        List<TSyncAppSystemManage> result = ListCopyUtil.copy(dataList, TSyncAppSystemManage.class);
        return saveBatch(result);
    }

    @Override
    public boolean syncOrg(SyncOrgUserDTO syncOrgUserDTO) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        /*CscpOrg cscpOrg = cscpOrgService.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, syncOrgUserDTO.getOrgId()));
        if (syncOrgUserDTO.getIsAutoPushFlag()) {
            if (StringUtils.isNotEmpty(syncOrgUserDTO.getFlag()) && "delete".equals(syncOrgUserDTO.getFlag())) {
                cscpOrg = cscpOrgRepository.getOrgIgnoreDeleted(syncOrgUserDTO.getOrgId());
            }
        }*/
        CscpOrg cscpOrg = cscpOrgRepository.getOrgIgnoreDeleted(syncOrgUserDTO.getOrgId());
        if (cscpOrg == null || appSystemManage == null) {
            log.error("同步组织信息接口syncOrg，请求参数有误");
            return false;
        }

        // 增加VPN组织机构同步逻辑
        if (WestoneUasManageService.WESTONE_UAS_SYSTEM.equals(appSystemManage.getAppCode())) {
//            return westoneUasManageService.syncOrgToWestoneUasSystem(syncOrgUserDTO);
            return true;
        }
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_UNIT");
        SyncOrgUserModel syncOrgUserModel = new SyncOrgUserModel();
        if (SyncAppSystemEnum.WAI_BU.getCode() != appSystemManage.getInSystemFlag()) {
            throw new BusinessException("非内部系统请调用外部推送接口!");
        }
        //查询推送历史记录
        if (syncOrgUserDTO.getIsAutoPushFlag()) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
        } else {
            syncModel.setStrOperaType(this.setOrgStrOperaType(appSystemManage, cscpOrg.getId()));
        }
        if (StringUtils.isNotEmpty(cscpOrg.getStrId())) {
            syncOrgUserModel.setStrId(cscpOrg.getStrId());
            syncOrgUserModel.setStrTrustNo(cscpOrg.getStrTrustNo());
            if (cscpOrg.getParentId() == 0) {
                syncOrgUserModel.setStrParentId("-1");
                syncOrgUserModel.setStrParentTrustNo("-1");
            } else {
                CscpOrg parentOrg = cscpOrgService.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, cscpOrg.getParentId()));
                if (parentOrg != null && StringUtils.isNotEmpty(parentOrg.getStrId())) {
                    syncOrgUserModel.setStrParentId(parentOrg.getStrId());
                    syncOrgUserModel.setStrParentTrustNo(parentOrg.getStrTrustNo());
                } else {
                    syncOrgUserModel.setStrParentId(cscpOrg.getParentId().toString());
                    syncOrgUserModel.setStrParentTrustNo(cscpOrg.getParentId().toString());
                }
            }
        } else {
            syncOrgUserModel.setStrId(cscpOrg.getId().toString());
            syncOrgUserModel.setStrTrustNo(cscpOrg.getId().toString());
            if (cscpOrg.getParentId() == 0) {
                syncOrgUserModel.setStrParentId("-1");
                syncOrgUserModel.setStrParentTrustNo("-1");
            } else {
                CscpOrg parentOrg = cscpOrgService.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, cscpOrg.getParentId()));
                if (parentOrg != null && StringUtils.isNotEmpty(parentOrg.getStrId())) {
                    syncOrgUserModel.setStrParentId(parentOrg.getStrId());
                    syncOrgUserModel.setStrParentTrustNo(parentOrg.getStrTrustNo());
                } else {
                    syncOrgUserModel.setStrParentId(cscpOrg.getParentId().toString());
                    syncOrgUserModel.setStrParentTrustNo(cscpOrg.getParentId().toString());
                }
            }
        }
        syncOrgUserModel.setStrUnitName(cscpOrg.getOrgName());
        syncOrgUserModel.setDtCreatDate(DateUtils.dateTimeToString(cscpOrg.getCreateTime()));
        syncOrgUserModel.setStrUnitCode(cscpOrg.getOrgCode());
        syncOrgUserModel.setStrDescription(cscpOrg.getDescription());
        syncOrgUserModel.setStrEasyName(cscpOrg.getOrgAbbreviation());
        syncOrgUserModel.setStrUnitAddress("");//单位地址
        syncOrgUserModel.setStrUnitNet("");//单位网址
        syncOrgUserModel.setStrUnitEmail("");//单位邮箱
        syncOrgUserModel.setStrPostalCode("");//邮政编码
        syncOrgUserModel.setStrAreaCode(cscpOrg.getRegionCode());
        syncOrgUserModel.setStrUnitPhone("");//单位电话
        syncOrgUserModel.setStrUnitFax("");//传真
        syncOrgUserModel.setIntSort(cscpOrg.getOrderBy());
        syncOrgUserModel.setStrRelationUser("");//单位联络人

        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", syncOrgUserModel);
        log.info("同步机构信息至{}三方系统 请求报文： {}", appSystemManage.getAppName(), JSONObject.toJSONString(bodyParams));

        try {
            // 推送操作
            SyncCommonHttpDTO syncCommonHttpDTO = new SyncCommonHttpDTO();
            syncCommonHttpDTO.setAppSystemManage(appSystemManage);
            syncCommonHttpDTO.setCscpOrg(cscpOrg);
            syncCommonHttpDTO.setBodyParams(bodyParams);
            syncCommonHttpDTO.setSyncOrgUserDTO(syncOrgUserDTO);
            syncCommonHttpDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            this.commonHttpRequest(syncCommonHttpDTO, false);
            return true;
        } catch (Exception e) {
            log.error("同步机构信息请求失败：", e);
        }
        return false;
    }

    @Override
    public boolean syncUser(SyncOrgUserDTO syncOrgUserDTO) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        CscpUser cscpUser = cscpUserService.getById(syncOrgUserDTO.getUserId());
        if (cscpUser == null || appSystemManage == null) {
            log.error("同步用户信息接口syncUser，请求参数有误");
            return false;
        }
        // 增加VPN用户同步逻辑
        if (WestoneUasManageService.WESTONE_UAS_SYSTEM.equals(appSystemManage.getAppCode())) {
            boolean isDefaultOrg = true;
            return westoneUasManageService.syncUserToWestoneUasSystem(syncOrgUserDTO, isDefaultOrg);
        }
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_USER");
        SyncOrgUserModel syncOrgUserModel = new SyncOrgUserModel();
        if (SyncAppSystemEnum.WAI_BU.getCode() != appSystemManage.getInSystemFlag()) {
            throw new BusinessException("非内部系统请调用外部推送接口!");
        }
        //查询推送历史记录
        if (syncOrgUserDTO.getIsAutoPushFlag()) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
        } else {
            syncModel.setStrOperaType(this.setUserStrOperaType(appSystemManage, cscpUser.getId()));
        }
        if ("delete".equals(syncModel.getStrOperaType())) {
            String appCodes = sysConfigService.getSysConfigValueByCode("push:delete:user:appCode");
            if (StringUtils.isNotEmpty(appCodes)) {
                if (!Arrays.asList(appCodes.split(",")).contains(appSystemManage.getAppCode())) {
                    return false;
                }
            }
        } else {
            // 新增、修改增加角色推送
            List<CscpRoles> list = cscpUserRoleRepository.queryRoleByUserId(cscpUser.getId());
            if (CollectionUtils.isNotEmpty(list)) {
                String roleIds = list.stream().map(CscpRoles::getId).map(String::valueOf).collect(Collectors.joining(","));
                syncOrgUserModel.setRoleIdsStr(roleIds);
                if (roleIds.contains(SystemRole.COMPANY_ROLE.getId())) {
                    syncOrgUserModel.setUnitAdminFlag("1");
                } else {
                    syncOrgUserModel.setUnitAdminFlag("0");
                }
            }
        }
        if (StringUtils.isEmpty(cscpUser.getStrId())) {
            syncOrgUserModel.setStrId(cscpUser.getId().toString());
        } else {
            syncOrgUserModel.setStrId(cscpUser.getStrId());
        }
        syncOrgUserModel.setStrUserId(cscpUser.getLoginName());
        if (StringUtils.isEmpty(cscpUser.getStrClassified()) && StringUtils.isNotEmpty(cscpUser.getSecurityClassificationCode())) {
            try {
                syncOrgUserModel.setStrClassified(((Integer.parseInt(cscpUser.getSecurityClassificationCode()) + 1) * 10) + "");
            } catch (NumberFormatException e) {
                log.error("同步用户信息接口syncUser，用户密级转换失败：", e);
            }
        } else {
            syncOrgUserModel.setStrClassified(cscpUser.getStrClassified());
        }
        syncOrgUserModel.setStrCname(cscpUser.getRealName());
        syncOrgUserModel.setStrEmail(cscpUser.getEmail());
        syncOrgUserModel.setStrMobile(cscpUser.getMobile());
        syncOrgUserModel.setStrMobileOther(cscpUser.getMobile());
        syncOrgUserModel.setStrFax("");//传真
        syncOrgUserModel.setStrOPhone(cscpUser.getOfficePhone());
        syncOrgUserModel.setStrPostalCode("");//邮编
        syncOrgUserModel.setStrPosttalAddress("");//联系地址
        syncOrgUserModel.setStrHPhone("");//住宅号码
        syncOrgUserModel.setStrHomeAdd("");//住宅地址
        syncOrgUserModel.setStrFlag("");//单位电话
        if (cscpUser.getSex() == null) {
            syncOrgUserModel.setStrSex("未知");
        } else if (cscpUser.getSex() == 0) {
            syncOrgUserModel.setStrSex("男");
        } else {
            syncOrgUserModel.setStrSex("女");
        }
        syncOrgUserModel.setStrMsnQq("");//MSN/QQ
        syncOrgUserModel.setStrNickName("");//昵称
        syncOrgUserModel.setStrTrustNo(cscpUser.getStrId());
        syncOrgUserModel.setStrDuty("");//职位
        syncOrgUserModel.setStrPost("");//岗位
        syncOrgUserModel.setStrElecCAN("");//电子认证号
        syncOrgUserModel.setStrMTSID("");//移动终端序列号
        syncOrgUserModel.setStrIdCardNo(cscpUser.getIdCardNo());
        //查询用户所属机构
        LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper();
        userOrgQW.eq(CscpUserOrg::getUserId, cscpUser.getId());
        // 移除 defaultDepartment 限制，以便查询所有机构
        // userOrgQW.eq(CscpUserOrg::getDefaultDepartment, 1);
        userOrgQW.orderByAsc(CscpUserOrg::getOrderBy);
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(userOrgQW);
        if (cscpUserOrgs != null && !cscpUserOrgs.isEmpty()) {
            List<SyncUserUnitModel> userUnitModels = new ArrayList<>();

            // 遍历所有用户机构关系
            for (CscpUserOrg userOrg : cscpUserOrgs) {
                Long orgId = userOrg.getOrgId();
                CscpOrg cscpOrg = cscpOrgService.getById(orgId);

                if (cscpOrg != null) {  // 添加空值检查
                    SyncUserUnitModel userUnitModel = new SyncUserUnitModel();

                    // 设置机构信息
                    if (StringUtils.isNotEmpty(cscpOrg.getStrId())) {
                        syncOrgUserModel.setStrUnitTrustNo(cscpOrg.getStrTrustNo());
                        userUnitModel.setStrUnitId(cscpOrg.getStrId());
                    } else {
                        syncOrgUserModel.setStrUnitTrustNo(cscpOrg.getId().toString());
                        userUnitModel.setStrUnitId(cscpOrg.getId().toString());
                    }

                    userUnitModel.setIntSort(cscpOrg.getOrderBy());
                    userUnitModel.setDefaultDepartment(userOrg.getDefaultDepartment() == null ? 0 : userOrg.getDefaultDepartment());
                    userUnitModels.add(userUnitModel);
                }
            }

            // 如果有有效的机构信息，则设置到 syncOrgUserModel 中
            if (!userUnitModels.isEmpty()) {
                syncOrgUserModel.setUnits(userUnitModels);
            }
        }

        // 配置是否单位管理员
        /*String unitAdminFlag = syncOrgUserDTO.getUnitAdminFlag();
        if (StrUtil.isBlank(syncOrgUserDTO.getUnitAdminFlag())) {
            LambdaQueryWrapper<CscpUserOrg> query = new LambdaQueryWrapper();
            query.eq(CscpUserOrg::getUserId, syncOrgUserDTO.getUserId());
            query.apply("org_id = company_id");
            query.last("limit 1");
            CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(query);
            if (cscpUserOrg != null && StrUtil.isNotBlank(cscpUser.getLoginName()) && cscpUser.getLoginName().contains("admin")) {
                unitAdminFlag = "1";
            }else{
                unitAdminFlag="0";
            }
        }
        syncOrgUserModel.setUnitAdminFlag(unitAdminFlag);*/

        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", syncOrgUserModel);
        log.info("同步用户信息至{}三方系统 请求报文： {}", appSystemManage.getAppName(), JSONObject.toJSONString(bodyParams));
        try {
            // 推送操作
            SyncCommonHttpDTO syncCommonHttpDTO = new SyncCommonHttpDTO();
            syncCommonHttpDTO.setAppSystemManage(appSystemManage);
            syncCommonHttpDTO.setBodyParams(bodyParams);
            syncCommonHttpDTO.setSyncOrgUserDTO(syncOrgUserDTO);
            if (syncOrgUserDTO.getCscpUserDetail() != null) {
                syncCommonHttpDTO.setCscpUserDetail(syncOrgUserDTO.getCscpUserDetail());
            } else {
                syncCommonHttpDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            }
            this.commonHttpRequest(syncCommonHttpDTO, false);
            return true;
        } catch (Exception e) {
            log.error("同步用户信息请求接口失败：", e);
        }
        return false;
    }

    /**
     * syncUser方法初步优化后
     *
     * @param syncOrgUserDTO
     * @param cscpUser
     * @return
     */
    private boolean syncOutUser(SyncOrgUserDTO syncOrgUserDTO, CscpUserDTO cscpUser) {
        TSyncAppSystemManage appSystemManage = syncOrgUserDTO.getAppSystemManage();
        // 1. 特殊逻辑：卫士通系统同步
        if (WestoneUasManageService.WESTONE_UAS_SYSTEM.equals(appSystemManage.getAppCode())) {
            boolean isDefaultOrg = true;
            return westoneUasManageService.syncUserToWestoneUasSystem(syncOrgUserDTO, isDefaultOrg);
        }

        if (cscpUser == null) {
            log.error("同步用户信息接口syncOutUser，查不到用户信息：" + syncOrgUserDTO.getUserId());
            return false;
        }
        cscpUser.setStrIdCardNo(cscpUser.getIdCardNo());

        // 3. 构建 SyncModel 和 SyncOrgUserModel
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_USER");

        // 设置操作类型
        syncModel.setStrOperaType(syncOrgUserDTO.getIsAutoPushFlag() ? syncOrgUserDTO.getFlag() : setUserStrOperaType(appSystemManage, cscpUser.getId()));

        // 构建用户模型数据
        SyncOrgUserModel syncOrgUserModel = buildSyncOrgUserModel(cscpUser);

        // 4. 角色信息填充
        enrichUserRoleInfo(syncOrgUserModel, cscpUser.getId());

        // 5. 构造请求参数 bodyParams
        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", syncOrgUserModel);

        log.info("同步用户信息至{}三方系统 请求报文： {}", appSystemManage.getAppName(), JSONObject.toJSONString(bodyParams));

        try {
            // 6. 发起 HTTP 请求
            SyncCommonHttpDTO syncCommonHttpDTO = new SyncCommonHttpDTO();
            syncCommonHttpDTO.setAppSystemManage(appSystemManage);
            syncCommonHttpDTO.setBodyParams(bodyParams);
            syncCommonHttpDTO.setSyncOrgUserDTO(syncOrgUserDTO);
            syncCommonHttpDTO.setCscpUserDto(cscpUser);
            // 设置用户信息方便后续使用
            if (syncOrgUserDTO.getCscpUserDetail() != null) {
                syncCommonHttpDTO.setCscpUserDetail(syncOrgUserDTO.getCscpUserDetail());
            } else {
                syncCommonHttpDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            }

            // 外部的少
            return commonHttpRequestNew(syncCommonHttpDTO);
        } catch (Exception e) {
            log.error("同步用户信息请求接口失败：", e);
        }
        return false;

    }

    private CscpUserDTO resolveCscpUser(SyncOrgUserDTO dto) {
        CscpUser cscpUser = cscpUserService.getById(dto.getUserId());
        CscpUserDTO cscpUserDTO = BeanConvertUtils.copyProperties(cscpUser, CscpUserDTO.class);
        if (cscpUserDTO != null && "delete".equals(dto.getFlag())) {
            // 如果是删除操作，则将用户状态设置为0
            cscpUserDTO.setStatus(0);
        }
        return cscpUserDTO;
    }

    private SyncOrgUserModel buildSyncOrgUserModel(CscpUserDTO cscpUser) {
        SyncOrgUserModel model = new SyncOrgUserModel();
        if (StringUtils.isNotEmpty(cscpUser.getStrId())) {
            model.setStrId(cscpUser.getStrId());
        } else {
            model.setStrId(cscpUser.getId().toString());
        }
        model.setStrUserId(cscpUser.getLoginName());
        model.setStrClassified(cscpUser.getStrClassified());
        model.setStrCname(cscpUser.getRealName());
        model.setStrEmail(cscpUser.getEmail());
        model.setStrMobile(cscpUser.getMobile());
        model.setStrOPhone(cscpUser.getOfficePhone());
        model.setStrSex(cscpUser.getSex() == null ? "未知" : (cscpUser.getSex() == 0 ? "男" : "女"));
        model.setStrIdCardNo(cscpUser.getIdCardNo());
        model.setStrTrustNo(cscpUser.getStrId());

        // 机构信息
        List<CscpUserOrg> userOrgs = getUserOrgs(cscpUser.getId());

        if (CollUtil.isNotEmpty(userOrgs)) {

            List<Long> orgIds = userOrgs.stream().map(CscpUserOrg::getOrgId).collect(Collectors.toList());
            List<CscpOrg> orgs = cscpOrgService.listByIds(orgIds);
            Map<Long, CscpOrg> orgMap = orgs.stream()
                    .collect(Collectors.toMap(CscpOrg::getId, o -> o));

            List<SyncUserUnitModel> unitModels = new ArrayList<>();
            for (CscpUserOrg userOrg : userOrgs) {
                CscpOrg org = orgMap.get(userOrg.getOrgId());
                if (org != null) {
                    SyncUserUnitModel unitModel = new SyncUserUnitModel();
                    unitModel.setStrUnitId(StringUtils.isNotEmpty(org.getStrId()) ? org.getStrId() : org.getId().toString());
                    unitModel.setIntSort(userOrg.getOrderBy());
                    unitModel.setDefaultDepartment(userOrg.getDefaultDepartment() != null ? userOrg.getDefaultDepartment() : 0);
                    unitModels.add(unitModel);
                }
            }
            model.setUnits(unitModels);
        }

        return model;
    }

    private List<CscpUserOrg> getUserOrgs(Long userId) {
        LambdaQueryWrapper<CscpUserOrg> wrapper = Wrappers.lambdaQuery(CscpUserOrg.class).eq(CscpUserOrg::getUserId, userId);
        return cscpUserOrgService.selectListNoAdd(wrapper);
    }

    private void enrichUserRoleInfo(SyncOrgUserModel model, Long userId) {
        List<CscpRoles> roles = cscpUserRoleRepository.queryRoleByUserId(userId);
        if (CollectionUtils.isNotEmpty(roles)) {
            String roleIds = roles.stream()
                    .map(role -> String.valueOf(role.getId()))
                    .collect(Collectors.joining(","));
            model.setRoleIdsStr(roleIds);
            model.setUnitAdminFlag(roleIds.contains(SystemRole.COMPANY_ROLE.getId()) ? "1" : "0");
        }
    }

    @Override
    public Pair<Boolean, String> syncOrgInSystem(SyncOrgUserDTO syncOrgUserDTO) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        CscpOrg cscpOrg = cscpOrgRepository.getOrgIgnoreDeleted(syncOrgUserDTO.getOrgId());
        if (cscpOrg == null || appSystemManage == null) {
            String errorMsg = "内部系统同步组织信息接口syncOrgInSystem，请求参数有误";
            log.error(errorMsg);
            return Pair.of(false, errorMsg);
        }

//        boolean checkSyncOrgFlag = this.checkSyncOrg(appSystemManage, cscpOrg);
//        if (!checkSyncOrgFlag) {
//            String errorMsg = "内部系统同步组织信息接口syncOrgInSystem，推送机构不在应用绑定机构范围内!";
//            log.error(errorMsg);
//            return Pair.of(false, errorMsg);
//        }

        if (StrUtil.equals(OrgUserConstants.UnitType.SWBGTCODE, cscpOrg.getOrgCode()) ||
                (cscpOrg.getOrgCode() != null && cscpOrg.getOrgCode().contains(OrgUserConstants.UnitType.SWBGTCODE))) {
            cscpOrg.setUnitFlag("省委办公厅");
        } else {
            cscpOrg.setUnitFlag("");
        }
        if (StrUtil.isBlank(cscpOrg.getOrgAbbreviation())) {
            cscpOrg.setOrgAbbreviation(cscpOrg.getOrgName());
        }
        // 内设机构需要配置上级的单位id及单位统一社会信用代码
        cscpOrgService.handleOrgBelongCompanyInfo(cscpOrg);

        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_UNIT");
        Map<String, Object> bodyParams = new HashMap<>();
        if (SyncAppSystemEnum.NEI_BU.getCode() != appSystemManage.getInSystemFlag()) {
            throw new BusinessException("外部接口请勿调用内部推送方法!");
        }
        //查询推送历史记录
        if (syncOrgUserDTO.getIsAutoPushFlag()) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
        } else {
            syncModel.setStrOperaType(this.setOrgStrOperaType(appSystemManage, cscpOrg.getId()));
        }
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpOrg);
        log.info("内部系统{}同步机构信息 请求报文： {}", appSystemManage.getAppName(), JSONObject.toJSONString(bodyParams));
        try {
            // 推送操作
            SyncCommonHttpDTO syncCommonHttpDto = new SyncCommonHttpDTO();
            syncCommonHttpDto.setAppSystemManage(appSystemManage);
            syncCommonHttpDto.setCscpOrg(cscpOrg);
            syncCommonHttpDto.setBodyParams(bodyParams);
            syncCommonHttpDto.setSyncOrgUserDTO(syncOrgUserDTO);
            syncCommonHttpDto.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            this.commonHttpRequest(syncCommonHttpDto, false);
            return Pair.of(true, null);
        } catch (Exception e) {
            String errorMsg = "内部系统" + appSystemManage.getAppName() + ",同步机构信息请求接口失败：" + e.getMessage();
            log.error(errorMsg, e);
            return Pair.of(false, errorMsg);
        }
    }

    @Override
    public Pair<Boolean, String> syncUserInSystem(SyncOrgUserDTO syncOrgUserDTO) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        CscpUser cscpUser = cscpUserService.getById(syncOrgUserDTO.getUserId());
        if (cscpUser == null || appSystemManage == null) {
            String errorMsg = "内部系统同步用户信息接口syncUserInSystem，请求参数有误";
            log.error(errorMsg);
            return Pair.of(false, errorMsg);
        }
        //查询用户所属机构
        LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper();
        userOrgQW.eq(CscpUserOrg::getUserId, cscpUser.getId());
        userOrgQW.orderByAsc(CscpUserOrg::getOrderBy);
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(userOrgQW);
        CscpUserDTO cscpUserDTO = BeanConvertUtils.copyProperties(cscpUser, CscpUserDTO.class);
        cscpUserDTO.setStrIdCardNo(cscpUser.getIdCardNo());
        // 新增推送用户、默认无需审核
        cscpUserDTO.setExamineStatus(1);

        if (CollUtil.isNotEmpty(cscpUserOrgs) && OrgUserConstants.UnitType.SWBGTID.equals(cscpUserOrgs.get(0).getCompanyId())) {
            cscpUserDTO.setUnitFlag("省委办公厅");
        } else {
            cscpUserDTO.setUnitFlag("");
        }

        // 设置用户所属机构、机构类型，所在单位统一社会信用代码
        this.handleFillOrgInfo(cscpUserOrgs, cscpUserDTO);

        String push = sysConfigService.getSysConfigValueByCode(SysConfigConstant.PUSH_DEFAULT_PASSWORD);
        if (StrUtil.isNotBlank(push) && "0".equals(push)) {
            // 当push为空或"0"时，清空密码
            cscpUserDTO.setPassword(null);
        }

//        boolean checkSyncUserFlag = this.checkSyncUser(appSystemManage, cscpUserDTO);
//        if (!checkSyncUserFlag) {
//            String errorMsg = "内部系统同步用户信息接口syncUserInSystem，推送用户不在应用绑定机构范围内!";
//            log.error(errorMsg);
//            return Pair.of(false, errorMsg);
//        }

        LambdaQueryWrapper<TAddressBook> lambdaQueryWrapper = new LambdaQueryWrapper<TAddressBook>();
        lambdaQueryWrapper.eq(TAddressBook::getUserId, cscpUser.getId());
        TAddressBook tAddressBook = tAddressBookService.selectOneNoAdd(lambdaQueryWrapper);
        if (tAddressBook != null) {
            cscpUserDTO.setSecretaryPhone(tAddressBook.getSecretaryPhone());
            cscpUserDTO.setSecretaryName(tAddressBook.getSecretaryName());
        }
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_USER");
        Map<String, Object> bodyParams = new HashMap<>();
        if (SyncAppSystemEnum.NEI_BU.getCode() != appSystemManage.getInSystemFlag()) {
            throw new BusinessException("外部接口请勿调用内部推送方法!");
        }
        //查询推送历史记录
        if (syncOrgUserDTO.getIsAutoPushFlag()) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
        } else {
            syncModel.setStrOperaType(this.setUserStrOperaType(appSystemManage, cscpUser.getId()));
        }
        // 新增、修改增加角色推送
        List<CscpRoles> list = cscpUserRoleRepository.queryRoleByUserId(cscpUser.getId());
        if (CollectionUtils.isNotEmpty(list)) {
            String roleIds = list.stream().map(CscpRoles::getId).map(String::valueOf).collect(Collectors.joining(","));
            cscpUserDTO.setRoleIdsStr(roleIds);
            if (roleIds.contains(SystemRole.COMPANY_ROLE.getId())) {
                cscpUserDTO.setUnitAdminFlag("1");
            } else {
                cscpUserDTO.setUnitAdminFlag("0");
            }
        }
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpUserDTO);
        log.info("内部系统{}同步用户信息 请求报文： {}", appSystemManage.getAppName(), JSONObject.toJSONString(bodyParams));
        try {
            SyncCommonHttpDTO syncCommonHttpDTO = new SyncCommonHttpDTO();
            syncCommonHttpDTO.setAppSystemManage(appSystemManage);
            syncCommonHttpDTO.setCscpUserDto(cscpUserDTO);
            syncCommonHttpDTO.setBodyParams(bodyParams);
            syncCommonHttpDTO.setSyncOrgUserDTO(syncOrgUserDTO);
            if (syncOrgUserDTO.getCscpUserDetail() != null) {
                syncCommonHttpDTO.setCscpUserDetail(syncOrgUserDTO.getCscpUserDetail());
            } else {
                syncCommonHttpDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            }
            this.commonHttpRequest(syncCommonHttpDTO, false);
            return Pair.of(true, null);
        } catch (Exception e) {
            String errorMsg = "内部系统" + appSystemManage.getAppName() + ",同步用户信息请求接口失败：" + e.getMessage();
            log.error(errorMsg, e);
            return Pair.of(false, errorMsg);
        }
    }

    public boolean syncInUser(SyncOrgUserDTO syncOrgUserDTO, CscpUserDTO cscpUser) {
        TSyncAppSystemManage appSystemManage = syncOrgUserDTO.getAppSystemManage();

        if (cscpUser == null) {
            log.error("同步用户信息接口syncOutUser，查不到用户信息：" + syncOrgUserDTO.getUserId());
            return false;
        }
        cscpUser.setStrIdCardNo(cscpUser.getIdCardNo());
        cscpUser.setExamineStatus(1);
        LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper();
        userOrgQW.eq(CscpUserOrg::getUserId, cscpUser.getId());
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(userOrgQW);
        cscpUser.setOrgList(cscpUserOrgs);
        if (CollUtil.isNotEmpty(cscpUserOrgs) && OrgUserConstants.UnitType.SWBGTID.equals(cscpUserOrgs.get(0).getCompanyId())) {
            cscpUser.setUnitFlag("省委办公厅");
        } else {
            cscpUser.setUnitFlag("");
        }
        String push = sysConfigService.getSysConfigValueByCode(SysConfigConstant.PUSH_DEFAULT_PASSWORD);
        if (StrUtil.isNotBlank(push) && "0".equals(push)) {
            // 当push为空或"0"时，清空密码
            cscpUser.setPassword(null);
        }
        // 角色信息填充
        List<CscpRoles> list = cscpUserRoleRepository.queryRoleByUserId(cscpUser.getId());
        if (CollectionUtils.isNotEmpty(list)) {
            String roleIds = list.stream().map(CscpRoles::getId).map(String::valueOf).collect(Collectors.joining(","));
            cscpUser.setRoleIdsStr(roleIds);
            if (roleIds.contains(SystemRole.COMPANY_ROLE.getId())) {
                cscpUser.setUnitAdminFlag("1");
            } else {
                cscpUser.setUnitAdminFlag("0");
            }
        }

        // 构建 SyncModel 和 SyncOrgUserModel
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_USER");
        // 设置操作类型
        syncModel.setStrOperaType(syncOrgUserDTO.getIsAutoPushFlag() ? syncOrgUserDTO.getFlag() : setUserStrOperaType(appSystemManage, cscpUser.getId()));
        // 构造请求参数 bodyParams
        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpUser);

        try {
            SyncCommonHttpDTO syncCommonHttpDTO = new SyncCommonHttpDTO();
            syncCommonHttpDTO.setAppSystemManage(appSystemManage);
            syncCommonHttpDTO.setCscpUserDto(cscpUser);
            syncCommonHttpDTO.setBodyParams(bodyParams);
            syncCommonHttpDTO.setSyncOrgUserDTO(syncOrgUserDTO);
            if (syncOrgUserDTO.getCscpUserDetail() != null) {
                syncCommonHttpDTO.setCscpUserDetail(syncOrgUserDTO.getCscpUserDetail());
            } else {
                syncCommonHttpDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            }
            // 执行
            return commonHttpRequestNew(syncCommonHttpDTO);
        } catch (Exception e) {
            log.error("syncInUser error :" + e);
            return false;
        }
    }


    /**
     * TODO 修改-禁用用户的推送业务平台的临时方法-后续改MQ
     * 修改、禁用操作回调此方法,根据推送用户记录表已有记录，进行后续推送操作
     * @param syncOrgUserDTO
     */
    @Override
    @SuppressWarnings("all")
    public void syncUserBusiness(SyncOrgUserDTO syncOrgUserDTO) {
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_USER");
        Map<String, Object> bodyParams = new HashMap<>();
        CscpUserDTO cscpUserDTO = new CscpUserDTO();
        log.info("进入同步推送: {}", JSONObject.toJSONString(syncOrgUserDTO));
        if (null == syncOrgUserDTO.getFlag()) {
            return;
        }
        if ("add".equals(syncOrgUserDTO.getFlag())) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
            cscpUserDTO = BeanConvertUtils.copyProperties(
                    cscpUserService.getById(syncOrgUserDTO.getUserId()), CscpUserDTO.class);
            // 新增推送用户、默认无需审核
            cscpUserDTO.setExamineStatus(1);
        }
        if ("update".equals(syncOrgUserDTO.getFlag())) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
            cscpUserDTO = BeanConvertUtils.copyProperties(
                    cscpUserService.getById(syncOrgUserDTO.getUserId()), CscpUserDTO.class);
        }
        cscpUserDTO.setStrIdCardNo(cscpUserDTO.getIdCardNo());
        // 用户删除已改为禁用
        if ("delete".equals(syncOrgUserDTO.getFlag())) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
            cscpUserDTO = BeanConvertUtils.copyProperties(
                    cscpUserService.getById(syncOrgUserDTO.getUserId()), CscpUserDTO.class);
        } else {
            // 新增、修改增加角色推送
            List<CscpRoles> list = cscpUserRoleRepository.queryRoleByUserId(syncOrgUserDTO.getUserId());
            if (CollectionUtils.isNotEmpty(list)) {
                String roleIds = list.stream().map(CscpRoles::getId).map(String::valueOf).collect(Collectors.joining(","));
                cscpUserDTO.setRoleIdsStr(roleIds);
                if (roleIds.contains(SystemRole.COMPANY_ROLE.getId())) {
                    cscpUserDTO.setUnitAdminFlag("1");
                } else {
                    cscpUserDTO.setUnitAdminFlag("0");
                }
            }
        }
        if (null == cscpUserDTO) {
            return;
        }
        //查询用户所属机构
        LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper();
        userOrgQW.eq(CscpUserOrg::getUserId, cscpUserDTO.getId());
        userOrgQW.orderByAsc(CscpUserOrg::getOrderBy);
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(userOrgQW);
        if (cscpUserDTO != null) {
            cscpUserDTO.setOrgList(cscpUserOrgs);
            if (StringUtils.isEmpty(cscpUserDTO.getStrId())) {
                cscpUserDTO.setStrId(cscpUserDTO.getId().toString());
            }
        }

        if (CollUtil.isNotEmpty(cscpUserOrgs) && OrgUserConstants.UnitType.SWBGTID.equals(cscpUserOrgs.get(0).getCompanyId())) {
            cscpUserDTO.setUnitFlag("省委办公厅");
        } else {
            cscpUserDTO.setUnitFlag("");
        }
        // 设置用户所属机构、机构类型，所在单位统一社会信用代码
        this.handleFillOrgInfo(cscpUserOrgs, cscpUserDTO);

        String push = sysConfigService.getSysConfigValueByCode(SysConfigConstant.PUSH_DEFAULT_PASSWORD);
        if (StrUtil.isNotBlank(push) && "0".equals(push)) {
            // 当push为空或"0"时，清空密码
            cscpUserDTO.setPassword(null);
        }
        List<TSyncAppSystemManage> appSystemManages;
        if (StringUtils.isNotEmpty(cscpUserDTO.getPushAppCode())) {
            appSystemManages = tSyncAppSystemManageMapper.selectListNoAdd(
                    Wrappers.lambdaQuery(TSyncAppSystemManage.class).eq(TSyncAppSystemManage::getStatus, 1)
                            .in(TSyncAppSystemManage::getAppCode, Arrays.asList(cscpUserDTO.getPushAppCode().split(",")))
            );
        } else if (null == syncOrgUserDTO.getAppId()) {
            // 获取所有同步应用系统信息
            appSystemManages = this.existAppSystemManage();
        } else {
            // 获取所有同步应用系统信息
            appSystemManages = tSyncAppSystemManageMapper.selectListNoAdd(
                    Wrappers.lambdaQuery(TSyncAppSystemManage.class).
                            eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId())
            );
        }
        Map<Long, TSyncAppSystemManage> appMap = new HashMap<>();
        appSystemManages.stream().forEach(a -> {
            appMap.putIfAbsent(a.getId(), a);
        });
        // todo，先把导入的自动推送的存至于pushappcode
        if (StringUtils.isEmpty(cscpUserDTO.getPushAppCode())) {
            cscpUserService.updateAppCodesById(cscpUserDTO.getId(),
                    StringUtils.join(appSystemManages.stream()
                            .filter(tSyncAppSystemManage -> "1".equals(tSyncAppSystemManage.getAutoPush()))
                            .map(a -> a.getAppCode()).collect(Collectors.toList()), ","));
            /*appAuthHistoryService.saveAppAuthByUser(cscpUserDTO.getId(), cscpUserDTO.getLoginName(), appSystemManages.stream()
                    .filter(tSyncAppSystemManage -> "1".equals(tSyncAppSystemManage.getAutoPush()))
                    .map(a -> Long.valueOf(a.getId())).collect(Collectors.toList()));*/
        } else {
            if ("delete".equals(syncOrgUserDTO.getFlag())) {
                // 更改用戶pushAppCode
                this.updateUserserPushAppCode(cscpUserDTO.getId(), appSystemManages);
            }
        }
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpUserDTO);

        if (null != appMap) {
            CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
            // todo 通过syncOrgUserDTO传递cscpUserDetail
            syncOrgUserDTO.setCscpUserDetail(cscpUserDetail);
            SecurityContext securityContext = SecurityContextHolder.getContext();
            CscpUserDTO finalCscpUserDTO = cscpUserDTO;
            log.info("<=======用户同步推送开始 Start=======>");
            List<CompletableFuture<String>> futures = new ArrayList<>();
            appMap.entrySet().forEach(a -> {
                if (StringUtils.isEmpty(finalCscpUserDTO.getPushAppCode()) && !"1".equals(a.getValue().getAutoPush())) {
                    return;
                }
                // 卫士通在用户新增、编辑的接口内已经调用过，这里将它排除调防止重复调用
                if (WestoneUasManageService.WESTONE_UAS_SYSTEM.equals(a.getValue().getAppCode())) {
                    return;
                }
                boolean checkSyncFlag = this.checkSyncUser(a.getValue(), finalCscpUserDTO);
                if (!checkSyncFlag) {
                    return;
                }
                // 测试
                //processTaskUser(a.getValue(), syncOrgUserDTO, finalCscpUserDTO, bodyParams);
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        SecurityContextHolder.setContext(securityContext);
                        return processTaskUser(a.getValue(), syncOrgUserDTO, finalCscpUserDTO, bodyParams);
                    } finally {
                        SecurityContextHolder.clearContext();
                    }
                }, threadPool).exceptionally(ex -> {
                    log.error("任务执行失败，原因: {}", ex.getMessage(), ex);
                    // 返回null表示任务失败
                    return null;
                });
                futures.add(future);
            });

            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            allFutures.thenRun(() -> {
                List<String> results = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull) // 过滤掉失败的任务（返回null的任务）
                        .collect(Collectors.toList());
                results.forEach(r -> log.info("线程执行推送结果->> {}", r));
            }).exceptionally(ex -> {
                log.error("汇总任务执行失败，原因: {}", ex.getMessage(), ex);
                return null;
            });
            log.info("<=======用户同步推送结束 end=======>");
        }
    }

    private void syncUserBusinessNew(SyncOrgUserDTO syncOrgUserDTO) {
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_USER");
        Map<String, Object> bodyParams = new HashMap<>();

        CscpUserDTO cscpUserDTO = buildCscpUserDTO(syncOrgUserDTO, syncModel);
        if (cscpUserDTO == null) {
            return;
        }

        // 设置角色信息
        enrichUserRoleInfo(cscpUserDTO, syncOrgUserDTO.getUserId());

        // 构建机构信息
        List<CscpUserOrg> userOrgs = getUserOrgs(syncOrgUserDTO.getUserId());
        if (CollectionUtils.isNotEmpty(userOrgs)) {
            cscpUserDTO.setOrgList(userOrgs);
            handleUnitFlag(cscpUserDTO, userOrgs);
        }

        // 处理密码脱敏配置
        handlePasswordPushConfig(cscpUserDTO);

        // 获取应用系统列表
        List<TSyncAppSystemManage> appSystemManages = getAppSystems(syncOrgUserDTO.getAppId());
        Map<Long, TSyncAppSystemManage> appMap = convertToAppMap(appSystemManages);

        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpUserDTO);

        if (appMap != null && !appMap.isEmpty()) {
            processAsyncTasks(appMap, syncOrgUserDTO, cscpUserDTO, bodyParams);
        }
    }

    // 构造用户 DTO
    private CscpUserDTO buildCscpUserDTO(SyncOrgUserDTO dto, SyncModel syncModel) {
        String flag = dto.getFlag();
        if (flag == null) {
            return null;
        }

        CscpUser user = cscpUserService.getById(dto.getUserId());
        if (user == null) {
            return null;
        }

        CscpUserDTO cscpUserDTO = BeanConvertUtils.copyProperties(user, CscpUserDTO.class);
        if (cscpUserDTO == null) {
            return null;
        }
        cscpUserDTO.setStrIdCardNo(user.getIdCardNo());

        if ("add".equals(flag) || "update".equals(flag)) {
            cscpUserDTO.setExamineStatus(1); // 默认无需审核
        }

        syncModel.setStrOperaType(flag);
        return cscpUserDTO;
    }

    // 填充角色信息
    private void enrichUserRoleInfo(CscpUserDTO dto, Long userId) {
        List<CscpRoles> roles = cscpUserRoleRepository.queryRoleByUserId(userId);
        if (CollectionUtils.isNotEmpty(roles)) {
            String roleIds = roles.stream()
                    .map(role -> String.valueOf(role.getId()))
                    .collect(Collectors.joining(","));
            dto.setRoleIdsStr(roleIds);
            dto.setUnitAdminFlag(roleIds.contains(SystemRole.COMPANY_ROLE.getId()) ? "1" : "0");
        }
    }

    // 处理单位标识（省委办公厅）
    private void handleUnitFlag(CscpUserDTO dto, List<CscpUserOrg> userOrgs) {
        if (CollUtil.isNotEmpty(userOrgs) && OrgUserConstants.UnitType.SWBGTID.equals(userOrgs.get(0).getCompanyId())) {
            dto.setUnitFlag("省委办公厅");
        } else {
            dto.setUnitFlag("");
        }
    }

    // 处理是否推送密码
    private void handlePasswordPushConfig(CscpUserDTO dto) {
        String push = sysConfigService.getSysConfigValueByCode(SysConfigConstant.PUSH_DEFAULT_PASSWORD);
        if (StrUtil.isNotBlank(push) && "0".equals(push)) {
            dto.setPassword(null);
        }
    }

    // 获取应用系统列表
    private List<TSyncAppSystemManage> getAppSystems(Long appId) {
        if (appId == null) {
            return existAppSystemManage();
        }
        return tSyncAppSystemManageMapper.selectListNoAdd(
                Wrappers.lambdaQuery(TSyncAppSystemManage.class).eq(TSyncAppSystemManage::getId, appId));
    }

    // 转换为 Map<id, app>
    private Map<Long, TSyncAppSystemManage> convertToAppMap(List<TSyncAppSystemManage> list) {
        return list.stream().collect(Collectors.toMap(TSyncAppSystemManage::getId, a -> a));
    }

    // 异步处理推送任务
    private void processAsyncTasks(Map<Long, TSyncAppSystemManage> appMap,
                                   SyncOrgUserDTO syncOrgUserDTO,
                                   CscpUserDTO finalCscpUserDTO,
                                   Map<String, Object> bodyParams) {

        SecurityContext securityContext = SecurityContextHolder.getContext();
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        syncOrgUserDTO.setCscpUserDetail(cscpUserDetail);

        log.info("<=======用户同步推送开始 Start=======>");
        List<CompletableFuture<String>> futures = new ArrayList<>();

        appMap.forEach((id, app) -> {
            if (!"1".equals(app.getAutoPush())) {
                return;
            }
            if (WestoneUasManageService.WESTONE_UAS_SYSTEM.equals(app.getAppCode())) {
                return;
            }
            if (!checkSyncUser(app, finalCscpUserDTO)) {
                return;
            }

            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    SecurityContextHolder.setContext(securityContext);
                    return processTaskUser(app, syncOrgUserDTO, finalCscpUserDTO, bodyParams);
                } finally {
                    SecurityContextHolder.clearContext();
                }
            }, threadPool).exceptionally(ex -> {
                log.error("任务执行失败，原因: {}", ex.getMessage(), ex);
                return null;
            });

            futures.add(future);
        });

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        allFutures.thenRun(() -> {
            List<String> results = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            results.forEach(r -> log.info("线程执行推送结果->> {}", r));
        }).exceptionally(ex -> {
            log.error("汇总任务执行失败，原因: {}", ex.getMessage(), ex);
            return null;
        });

        log.info("<=======用户同步推送结束 End=======>");
    }


    @Override
    public List<Long> getAllUserByCompanyId(SyncOrgUserDTO syncOrgUserDTO) {
        List<Long> userIds = tSyncUserHistroyRecordMapper.queryCscpUserList(syncOrgUserDTO.getOrgId());
        return userIds;
    }

    @Override
    public Pair<Boolean, String> batchSyncOrgInSystem(List<SyncOrgUserDTO> syncOrgUserDTOList) {
        // syncOrgUserDTOList中先对appId和orgId进行去重
        if (CollUtil.isNotEmpty(syncOrgUserDTOList)) {
            syncOrgUserDTOList = syncOrgUserDTOList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    dto -> Arrays.asList(dto.getAppId(), dto.getOrgId()),
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));

            StringBuilder errorMsg = new StringBuilder();

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            SecurityContext securityContext = SecurityContextHolder.getContext();
            for (SyncOrgUserDTO syncOrgUserDTO : syncOrgUserDTOList) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    if (null == syncOrgUserDTO.getAppId() || syncOrgUserDTO.getOrgId() == null) {
                        synchronized (errorMsg) {
                            errorMsg.append("系统名称：").append(syncOrgUserDTO.getAppName()).append(" 机构名称：").append(syncOrgUserDTO.getStrUnitName())
                                    .append(" 推送失败：").append("appId和orgId不能为空");
                        }
                        return;
                    }

                    if (syncOrgUserDTO.getInSystemFlag() != null && syncOrgUserDTO.getInSystemFlag() == 0) {
                        boolean pair = syncOrg(syncOrgUserDTO);
                        if (!pair) {
                            synchronized (errorMsg) {
                                errorMsg.append("系统名称：").append(syncOrgUserDTO.getAppName()).append(" 机构名称：").append(syncOrgUserDTO.getStrUnitName())
                                        .append(" 推送失败");
                            }
                        }
                    } else {
                        Pair<Boolean, String> pair = syncOrgInSystem(syncOrgUserDTO);
                        if (!pair.getKey()) {
                            synchronized (errorMsg) {
                                errorMsg.append("系统名称：").append(syncOrgUserDTO.getAppName()).append(" 机构名称：").append(syncOrgUserDTO.getStrUnitName())
                                        .append(" 推送失败：").append(pair.getValue());
                            }
                        }
                    }
                });

                futures.add(future);
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            if (errorMsg.length() > 0) {
                return Pair.of(false, errorMsg.toString());
            }
        }

        return Pair.of(true, null);
    }

    @Override
    public Pair<Boolean, String> batchSyncUserInSystem(List<SyncOrgUserDTO> syncOrgUserDTOList) {
        // syncOrgUserDTOList中先对appId和userId进行去重
        if (CollUtil.isNotEmpty(syncOrgUserDTOList)) {
            syncOrgUserDTOList = syncOrgUserDTOList.stream()
                    .collect(Collectors.collectingAndThen(
                            Collectors.toMap(
                                    dto -> Arrays.asList(dto.getAppId(), dto.getUserId()),
                                    Function.identity(),
                                    (existing, replacement) -> existing
                            ),
                            map -> new ArrayList<>(map.values())
                    ));

            StringBuilder errorMsg = new StringBuilder();

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            SecurityContext securityContext = SecurityContextHolder.getContext();
            for (SyncOrgUserDTO syncOrgUserDTO : syncOrgUserDTOList) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    if (null == syncOrgUserDTO.getAppId() || syncOrgUserDTO.getUserId() == null) {
                        synchronized (errorMsg) {
                            errorMsg.append("系统名称：").append(syncOrgUserDTO.getAppName()).append(" 用户名：").append(syncOrgUserDTO.getLoginName())
                                    .append(" 推送失败：").append("appId和userId不能为空");
                        }
                        return;
                    }
                    if (syncOrgUserDTO.getInSystemFlag() != null && syncOrgUserDTO.getInSystemFlag() == 0) {
                        boolean pair = syncUser(syncOrgUserDTO);
                        if (!pair) {
                            synchronized (errorMsg) {
                                errorMsg.append("系统名称：").append(syncOrgUserDTO.getAppName()).append(" 用户名：").append(syncOrgUserDTO.getLoginName())
                                        .append(" 推送失败");
                            }
                        }
                    } else {
                        Pair<Boolean, String> pair = syncUserInSystem(syncOrgUserDTO);
                        if (!pair.getKey()) {
                            synchronized (errorMsg) {
                                errorMsg.append("系统名称：").append(syncOrgUserDTO.getAppName()).append(" 用户名：").append(syncOrgUserDTO.getLoginName())
                                        .append(" 推送失败：").append(pair.getValue());
                            }
                        }
                    }
                });

                futures.add(future);
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
            if (errorMsg.length() > 0) {
                return Pair.of(false, errorMsg.toString());
            }
        }

        return Pair.of(true, null);
    }

    private String processTaskUser(TSyncAppSystemManage appSystemManage,
                                   SyncOrgUserDTO syncOrgUserDTO,
                                   CscpUserDTO cscpUserDTO,
                                   Map<String, Object> bodyParams) {
        StringBuilder resultMsg = new StringBuilder(appSystemManage.getAppName());
        if (OrgUserConstants.RequestMode.REQUEST_MODE_MQ.equals(appSystemManage.getRequestMode())) {
            syncOrgUserDTO.setAppId(appSystemManage.getId());
            this.mqSyncUser(syncOrgUserDTO);
            resultMsg.append(">>>>执行用户-MQ推送<<<<");
            return resultMsg.toString();
        } else {
            Integer inSystemFlag = appSystemManage.getInSystemFlag();
            // 如果是外部系统推送-走之前的逻辑
            if (SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag) {
                syncOrgUserDTO.setAppId(appSystemManage.getId());
                syncOrgUserDTO.setUserId(cscpUserDTO.getId());
                syncOrgUserDTO.setIsAutoPushFlag(true);
                this.syncUser(syncOrgUserDTO);
                resultMsg.append(">>>>执行用户-HTTP外部推送<<<<");
            } else {
                if ("delete".equals(syncOrgUserDTO.getFlag())) {
                    String appCodes = sysConfigService.getSysConfigValueByCode("push:delete:user:appCode");
                    if (StringUtils.isNotEmpty(appCodes)) {
                        if (!Arrays.asList(appCodes.split(",")).contains(appSystemManage.getAppCode())) {
                            return resultMsg.toString();
                        }
                    }
                }
                SyncCommonHttpDTO syncCommonHttpDto = new SyncCommonHttpDTO();
                syncCommonHttpDto.setAppSystemManage(appSystemManage);
                syncCommonHttpDto.setCscpUserDto(cscpUserDTO);
                syncCommonHttpDto.setBodyParams(bodyParams);
                syncCommonHttpDto.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
                syncCommonHttpDto.setSyncOrgUserDTO(syncOrgUserDTO);
                this.commonHttpRequest(syncCommonHttpDto, false);
                resultMsg.append(">>>>执行用户-HTTP内部推送<<<<");
            }

        }
        return resultMsg.toString();
    }

    /**
     * TODO 修改-删除机构的推送业务平台的临时方法-后续改MQ
     * 修改、删除操作回调此方法,根据推送用户记录表已有记录，进行后续推送操作
     * @param syncOrgUserDTO
     */
    @Override
    @SuppressWarnings("all")
    public void syncOrgBusiness(SyncOrgUserDTO syncOrgUserDTO) {
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_UNIT");
        Map<String, Object> bodyParams = new HashMap<>();
        CscpOrg cscpOrg = new CscpOrg();
        if (null == syncOrgUserDTO.getFlag()) {
            return;
        }
        if ("add".equals(syncOrgUserDTO.getFlag())) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
            cscpOrg = cscpOrgService.getById(syncOrgUserDTO.getOrgId());
        }
        if ("update".equals(syncOrgUserDTO.getFlag())) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
            cscpOrg = cscpOrgService.getById(syncOrgUserDTO.getOrgId());
        }
        // 机构逻辑删除-直接传orgId过去
        if ("delete".equals(syncOrgUserDTO.getFlag())) {
            syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
            cscpOrg = cscpOrgRepository.getOrgIgnoreDeleted(syncOrgUserDTO.getOrgId());
        }

        if (StrUtil.equals(OrgUserConstants.UnitType.SWBGTCODE, cscpOrg.getOrgCode())) {
            cscpOrg.setUnitFlag("省委办公厅");
        } else {
            cscpOrg.setUnitFlag("");
        }
        if (StrUtil.isBlank(cscpOrg.getOrgAbbreviation())) {
            cscpOrg.setOrgAbbreviation(cscpOrg.getOrgName());
        }
        // 内设机构需要配置上级的单位id及单位统一社会信用代码
        cscpOrgService.handleOrgBelongCompanyInfo(cscpOrg);

        List<TSyncAppSystemManage> appSystemManages;
        if (StringUtils.isNotEmpty(cscpOrg.getPushAppCode())) {
            appSystemManages = tSyncAppSystemManageMapper.selectListNoAdd(
                    Wrappers.lambdaQuery(TSyncAppSystemManage.class).eq(TSyncAppSystemManage::getStatus, 1)
                            .in(TSyncAppSystemManage::getAppCode, Arrays.asList(cscpOrg.getPushAppCode().split(",")))
            );
        } else if (null == syncOrgUserDTO.getAppId()) {
            // 获取所有同步应用系统信息
            appSystemManages = this.existAppSystemManage();
        } else {
            // 获取所有同步应用系统信息
            appSystemManages = tSyncAppSystemManageMapper.selectListNoAdd(
                    Wrappers.lambdaQuery(TSyncAppSystemManage.class).
                            eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId())
            );
        }

        Map<Long, TSyncAppSystemManage> appMap = new HashMap<>();
        appSystemManages.stream().forEach(a -> {
            appMap.putIfAbsent(a.getId(), a);
        });
        // todo，先把自动推送的存至于pushappcode
        if (cscpOrg.getPushAppCode() == null) {
            cscpOrgService.updateAppCodesById(cscpOrg.getId(),
                    StringUtils.join(appSystemManages.stream()
                            .filter(tSyncAppSystemManage -> "1".equals(tSyncAppSystemManage.getAutoPush()))
                            .map(a -> a.getAppCode()).collect(Collectors.toList()), ","));
            /*appAuthHistoryService.saveAppAuthByOrg(cscpOrg.getId(), cscpOrg.getOrgName(), appSystemManages.stream()
                    .filter(tSyncAppSystemManage -> "1".equals(tSyncAppSystemManage.getAutoPush()))
                    .map(a -> Long.valueOf(a.getId())).collect(Collectors.toList()));*/
        }
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpOrg);
        if (null != appMap) {
            SecurityContext securityContext = SecurityContextHolder.getContext();
            CscpOrg finalCscpOrg = cscpOrg;
            log.info("<=======机构同步推送开始 Start=======>");
            List<CompletableFuture<String>> futures = new ArrayList<>();
            appMap.entrySet().forEach(a -> {
                if (!"1".equals(a.getValue().getAutoPush())) {
                    return;
                }

                boolean checkSyncFlag = this.checkSyncOrg(a.getValue(), finalCscpOrg);
                if (!checkSyncFlag) {
                    return;
                }

                // 在appMap循环里每次创建新的DTO实例，避免共享
                SyncOrgUserDTO finalSyncOrgUserDTO = new SyncOrgUserDTO();
                BeanUtils.copyProperties(syncOrgUserDTO, finalSyncOrgUserDTO);
                CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        SecurityContextHolder.setContext(securityContext);
                        return processTaskOrg(a.getValue(), finalSyncOrgUserDTO, finalCscpOrg, bodyParams);
                    } finally {
                        SecurityContextHolder.clearContext();
                    }
                }, threadPool).exceptionally(ex -> {
                    log.error("任务执行失败，原因: {}", ex.getMessage(), ex);
                    // 返回null表示任务失败
                    return null;
                });
                futures.add(future);
            });

            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0])
            );

            allFutures.thenRun(() -> {
                List<String> results = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull) // 过滤掉失败的任务（返回null的任务）
                        .collect(Collectors.toList());
                results.forEach(r -> log.info("线程执行推送结果->> {}", r));
            }).exceptionally(ex -> {
                log.error("汇总任务执行失败，原因: {}", ex.getMessage(), ex);
                return null;
            });
            log.info("<=======机构同步推送结束 End=======>");
        }
    }

    private String processTaskOrg(TSyncAppSystemManage appSystemManage,
                                  SyncOrgUserDTO syncOrgUserDTO,
                                  CscpOrg cscpOrg,
                                  Map<String, Object> bodyParams) {
        StringBuilder resultMsg = new StringBuilder(appSystemManage.getAppName());
        if (OrgUserConstants.RequestMode.REQUEST_MODE_MQ.equals(appSystemManage.getRequestMode())) {
            syncOrgUserDTO.setAppId(appSystemManage.getId());
            this.mqSyncOrgOne(cscpOrg, syncOrgUserDTO, appSystemManage);
            resultMsg.append(">>>>执行机构-MQ推送<<<<");
            return resultMsg.toString();
        } else {
            Integer inSystemFlag = appSystemManage.getInSystemFlag();
            // 如果是外部系统推送-走之前的逻辑
            if (SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag) {
                syncOrgUserDTO.setAppId(appSystemManage.getId());
                syncOrgUserDTO.setOrgId(cscpOrg.getId());
                syncOrgUserDTO.setIsAutoPushFlag(true);
                this.syncOrg(syncOrgUserDTO);
                resultMsg.append(">>>>执行机构-HTTP外部推送<<<<");
            } else {
                SyncCommonHttpDTO syncCommonHttpDto = new SyncCommonHttpDTO();
                syncCommonHttpDto.setAppSystemManage(appSystemManage);
                syncCommonHttpDto.setCscpOrg(cscpOrg);
                syncCommonHttpDto.setBodyParams(bodyParams);
                syncCommonHttpDto.setSyncOrgUserDTO(syncOrgUserDTO);
                syncCommonHttpDto.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
                this.commonHttpRequest(syncCommonHttpDto, false);
                resultMsg.append(">>>>执行机构-HTTP内部推送<<<<");
            }
        }
        return resultMsg.toString();
    }

    /**
     * 判断应用管理请求地址是为http
     * @param urlString
     * @return
     */
    public static boolean isValidHttpUrl(String urlString) {
        try {
            // 检查 URL 格式是否合法
            URL url = new URL(urlString);

            // 检查协议是否为 HTTP 或 HTTPS
            String protocol = url.getProtocol();
            if (!protocol.equals("http") && !protocol.equals("https")) {
                log.info("URL 协议必须是 HTTP 或 HTTPS");
                return false;
            }
            return true;
        } catch (MalformedURLException e) {
            log.error("URL 格式不合法: " + e.getMessage());
            return false;
        }
    }

    @Override
    @SuppressWarnings("all")
    @Transactional(rollbackFor = Exception.class)
    public void mqSyncUser(SyncOrgUserDTO syncOrgUserDTO) {
        log.info("<===========开启用户同步推送===========>");
        TSyncAppSystemManage appSystemManage = syncOrgUserDTO.getAppSystemManage();
        if (appSystemManage == null) {
            appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(new LambdaQueryWrapper<TSyncAppSystemManage>()
                    .eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        }
        if (appSystemManage == null) {
            return;
        }
        syncOrgUserDTO.setAppId(appSystemManage.getId());
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (null == cscpUserDetail) {
            cscpUserDetail = syncOrgUserDTO.getCscpUserDetail();
        }
        log.info("获取当前登录信息: {}", JSONObject.toJSONString(cscpUserDetail));
        // 校验
        this.ownedOrgValid(appSystemManage.getInOwnedOrgId(), syncOrgUserDTO);

        CscpUser cscpUser = cscpUserService.getById(syncOrgUserDTO.getUserId());
        if (null == cscpUser) {
            log.error("用户同步推送信息接口syncUser，请求参数有误");
        }
        // 处理机构
        this.existUserPush(syncOrgUserDTO, cscpUser);
        // 查询用户所属机构
        LambdaQueryWrapper<CscpUserOrg> userOrgQW = Wrappers.lambdaQuery();
        userOrgQW.eq(CscpUserOrg::getUserId, cscpUser.getId());
        userOrgQW.orderByAsc(CscpUserOrg::getOrderBy);
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(userOrgQW);
        CscpUserDTO cscpUserDTO = BeanConvertUtils.copyProperties(cscpUser, CscpUserDTO.class);
        if (cscpUserDTO != null) {
            if (CollUtil.isNotEmpty(cscpUserOrgs) && OrgUserConstants.UnitType.SWBGTID.equals(cscpUserOrgs.get(0).getCompanyId())) {
                cscpUserDTO.setUnitFlag("省委办公厅");
            } else {
                cscpUserDTO.setUnitFlag("");
            }

            // 设置用户所属机构、机构类型，所在单位统一社会信用代码
            this.handleFillOrgInfo(cscpUserOrgs, cscpUserDTO);

            String push = sysConfigService.getSysConfigValueByCode(SysConfigConstant.PUSH_DEFAULT_PASSWORD);
            if (StrUtil.isNotBlank(push) && "0".equals(push)) {
                // 当push为空或"0"时，清空密码
                cscpUserDTO.setPassword(null);
            }

            cscpUserDTO.setOrgList(cscpUserOrgs);
            if (StringUtils.isEmpty(cscpUserDTO.getStrId())) {
                cscpUserDTO.setStrId(cscpUserDTO.getId().toString());
            }
        }
        // 查询用户通讯录
        LambdaQueryWrapper<TAddressBook> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(TAddressBook::getUserId, cscpUser.getId());
        TAddressBook tAddressBook = tAddressBookService.selectOneNoAdd(lambdaQueryWrapper);
        if (tAddressBook != null) {
            cscpUserDTO.setSecretaryPhone(tAddressBook.getSecretaryPhone());
            cscpUserDTO.setSecretaryName(tAddressBook.getSecretaryName());
        }
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_USER");
        syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
        if ("delete".equals(syncModel.getStrOperaType())) {
            String appCodes = sysConfigService.getSysConfigValueByCode("push:delete:user:appCode");
            if (StringUtils.isNotEmpty(appCodes)) {
                if (!Arrays.asList(appCodes.split(",")).contains(appSystemManage.getAppCode())) {
                    return;
                }
            }
        }
        Map<String, Object> bodyParams = new HashMap<>();
        //插入推送记录表
        TSyncUserHistroyRecord insertRecord = this.setUserHistory(appSystemManage, cscpUserDTO, syncModel.getStrOperaType(), cscpUserDetail);
        // 手动设置create_time，确保分表路由正确
        if (insertRecord.getCreateTime() == null) {
            insertRecord.setCreateTime(LocalDateTime.now());
        }
        tSyncUserHistroyRecordMapper.insert(insertRecord);
        if (null != insertRecord.getId()) cscpUserDTO.setSyncHistoryId(String.valueOf(insertRecord.getId()));

        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpUserDTO);

        log.info("用户同步推送信息 请求报文： {}", JSONObject.toJSONString(bodyParams));
        userProducer.syncToProject(appSystemManage.getAppRegionCode(cscpOrgRepository), JSONObject.toJSONString(bodyParams));
        log.info("<===========结束用户同步推送===========>");
    }

    @Override
    public Pair<String, String> mqSyncOrg(SyncOrgUserDTO syncOrgUserDTO) {
        // 查询应用
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(new LambdaQueryWrapper<TSyncAppSystemManage>()
                .eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        if (appSystemManage == null) {
            return Pair.of(OrgUserConstants.SyncStatus.F, "应用数据有误，查询失败！");
        }
        // 校验
        this.ownedOrgValid(appSystemManage.getInOwnedOrgId(), syncOrgUserDTO);
        // 查询机构
        List<CscpOrg> orgList;
        if (CollectionUtils.isNotEmpty(syncOrgUserDTO.getOrgIds())) {
            // 页面选择的机构
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CscpOrg::getDeleted, 0);
            queryWrapper.in(CscpOrg::getId, syncOrgUserDTO.getOrgIds());
            queryWrapper.orderByAsc(CscpOrg::getParentId);
            orgList = cscpOrgRepository.selectList(queryWrapper);
        } else if (syncOrgUserDTO.getOrgId() == null && !SecurityUtils.isNormalName()) {
            // 非普通用户 - 管理员推送全部有效机构
            LambdaQueryWrapper<CscpOrg> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CscpOrg::getDeleted, 0);
            queryWrapper.orderByAsc(CscpOrg::getParentId);
            orgList = cscpOrgRepository.selectList(queryWrapper);
        } else {
            Long orgId = syncOrgUserDTO.getOrgId();
            if (orgId == null) {
                orgId = SecurityUtils.getCurrentCscpUserDetail().getCompanyId();
            }
            // 根据单位id查询所有上级单位（包括自己）
            orgList = cscpOrgRepository.selectSelf2RootOrg(orgId);
        }
        if (CollectionUtils.isEmpty(orgList)) {
            String errorMsg = "内部系统同步组织信息接口syncOrgInSystem，请求参数有误";
            log.error(errorMsg);
            return Pair.of(OrgUserConstants.SyncStatus.F, errorMsg);
        }

        StringBuilder errorMsg = new StringBuilder();
        AtomicInteger errorCount = new AtomicInteger(0);
        for (CscpOrg cscpOrg : orgList) {
            // 推送
            Pair<Boolean, String> pair = mqSyncOrgOne(cscpOrg, syncOrgUserDTO, appSystemManage);
            if (!pair.getKey()) {
                errorMsg.append(pair.getValue()).append(";");
                errorCount.getAndIncrement();
            }
        }
        if (errorCount.get() == orgList.size()) {
            return Pair.of(OrgUserConstants.SyncStatus.F, errorMsg.toString());
        } else if (errorMsg.length() > 0) {
            return Pair.of(OrgUserConstants.SyncStatus.P, errorMsg.toString());
        } else {
            return Pair.of(OrgUserConstants.SyncStatus.S, null);
        }

    }

    @Override
    public void mqSyncUserOfOrg(SyncOrgUserDTO syncOrgUser) {
        Assert.notNull(syncOrgUser.getAppId(), "appId can not be null");
        if (CollectionUtils.isEmpty(syncOrgUser.getOrgIds())) {
            throw new BusinessException("orgIds can not be empty");
        }

        for (Long orgId : syncOrgUser.getOrgIds()) {
            // 根据单位id查询所有上级单位（包括自己）
            List<CscpOrg> orgList = cscpOrgRepository.selectSelf2RootOrg(orgId);
            if (CollectionUtils.isEmpty(orgList)) {
                return;
            }
            // 自身机构推送校验
            LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TSyncOrgHistroyRecord::getOrgId, orgId);
            queryWrapper.eq(TSyncOrgHistroyRecord::getAppId, syncOrgUser.getAppId());
            queryWrapper.eq(TSyncOrgHistroyRecord::getSyncSuccess, "true");
            queryWrapper.eq(TSyncOrgHistroyRecord::getSyncStatus, "200");
            if (tSyncOrgHistroyRecordMapper.selectCount(queryWrapper) > 0) {
                // 当前机构推成功过，则return
                return;
            }
            // 市一级机构推送校验
            List<CscpOrg> cityOrgList = orgList.stream().filter(cscpOrg1 -> cscpOrg1.getOrgCode() != null
                    && cscpOrg1.getOrgCode().length() == 12).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(cityOrgList)) {
                List<Long> orgIds = cityOrgList.stream().map(CscpOrg::getId).collect(Collectors.toList());
                LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapper2 = new LambdaQueryWrapper<>();
                queryWrapper2.in(TSyncOrgHistroyRecord::getOrgId, orgIds);
                queryWrapper2.eq(TSyncOrgHistroyRecord::getAppId, syncOrgUser.getAppId());
                if (tSyncOrgHistroyRecordMapper.selectCount(queryWrapper2) > 0) {
                    // 当前机构没被推过 且 上级机构中 LENGTH(path_code) = 12 的机构被推过
                    TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(new LambdaQueryWrapper<TSyncAppSystemManage>()
                            .eq(TSyncAppSystemManage::getId, syncOrgUser.getAppId()));
                    // 推送市级下属直到本身的机构
                    List<CscpOrg> pushOrgList = orgList.stream().filter(cscpOrg1 -> cscpOrg1.getOrgCode() != null
                            && cscpOrg1.getOrgCode().length() > 12).collect(Collectors.toList());
                    for (CscpOrg cscpOrg : pushOrgList) {
                        // 省委办公厅推送标识
                        if (StrUtil.equals(OrgUserConstants.UnitType.SWBGTCODE, cscpOrg.getOrgCode()) ||
                                (cscpOrg.getOrgCode() != null && cscpOrg.getOrgCode().contains(OrgUserConstants.UnitType.SWBGTCODE))) {
                            cscpOrg.setUnitFlag("省委办公厅");
                        } else {
                            cscpOrg.setUnitFlag("");
                        }
                        if (StrUtil.isBlank(cscpOrg.getOrgAbbreviation())) {
                            cscpOrg.setOrgAbbreviation(cscpOrg.getOrgName());
                        }

                        mqSyncOrgOne(cscpOrg, syncOrgUser, appSystemManage);
                    }
                }
            }
        }

    }

    /**
     * 同步单个机构信息到应用系统，并推送单位管理员账号（如果适用）
     *
     * @param cscpOrg           机构对象
     * @param syncOrgUserDTO    同步组织用户DTO
     * @param appSystemManage   应用系统管理对象
     * @return 操作结果及错误信息
     */
    private Pair<Boolean, String> mqSyncOrgOne(CscpOrg cscpOrg, SyncOrgUserDTO syncOrgUserDTO, TSyncAppSystemManage appSystemManage) {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (cscpUserDetail == null) {
            cscpUserDetail = syncOrgUserDTO.getCscpUserDetail();
        }
        // 如果推送单位，需要同时推送单位管理员账号
        if (cscpOrg.getType() == 2) {
            SyncOrgUserDTO userDTO = new SyncOrgUserDTO();
            userDTO.setAppId(syncOrgUserDTO.getAppId());
            // 根据单位id查询单位管理员
            LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper<>();
            userOrgQW.eq(CscpUserOrg::getOrgId, syncOrgUserDTO.getOrgId())
                    .orderByAsc(CscpUserOrg::getUserId)
                    .last("limit 1");
            CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(userOrgQW);
            if (cscpUserOrg != null) {
                userDTO.setUserId(cscpUserOrg.getUserId());
                if (StringUtils.isNotEmpty(syncOrgUserDTO.getFlag())) {
                    userDTO.setFlag(syncOrgUserDTO.getFlag());
                }
                userDTO.setCscpUserDetail(cscpUserDetail);
                this.mqSyncUser(userDTO);
            }
        }

        // 新增推送记录
        TSyncOrgHistroyRecord insertRecord = this.setOrgHistory(appSystemManage, cscpOrg, syncOrgUserDTO.getFlag(), cscpUserDetail);
        if (StrUtil.isNotBlank(syncOrgUserDTO.getOperateType())) {
            insertRecord.setOperateType(syncOrgUserDTO.getOperateType());
        } else {
            insertRecord.setOperateType(AUTO_PUSH);
        }

        insertRecord.setSourceId(syncOrgUserDTO.getSourceId());
        // 手动设置create_time，确保分表路由正确
        if (insertRecord.getCreateTime() == null) {
            insertRecord.setCreateTime(LocalDateTime.now());
        }
        tSyncOrgHistroyRecordMapper.insert(insertRecord);
        cscpOrg.setSyncHistoryId(insertRecord.getId());
        if (StrUtil.equals(OrgUserConstants.UnitType.SWBGTCODE, cscpOrg.getOrgCode()) ||
                (cscpOrg.getOrgCode() != null && cscpOrg.getOrgCode().contains(OrgUserConstants.UnitType.SWBGTCODE))) {
            cscpOrg.setUnitFlag("省委办公厅");
        } else {
            cscpOrg.setUnitFlag("");
        }
        if (StrUtil.isBlank(cscpOrg.getOrgAbbreviation())) {
            cscpOrg.setOrgAbbreviation(cscpOrg.getOrgName());
        }

        // 构建同步模型和请求体
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_UNIT");
        syncModel.setStrOperaType(syncOrgUserDTO.getFlag());
        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpOrg);
        log.debug("内部系统同步机构信息 请求报文： {}", JSONObject.toJSONString(bodyParams));

        try {
            orgProducer.syncToProject(appSystemManage.getAppRegionCode(cscpOrgRepository), JSONObject.toJSONString(bodyParams));
            return Pair.of(true, null);
        } catch (Exception e) {
            String errorMsg = "内部系统同步机构信息请求失败：" + e.getMessage();
            log.error(errorMsg, e);
            return Pair.of(false, errorMsg);
        }
    }

    @Override
    public List<TSyncAppSystemManage> getActiveProjectsUser() {
        LambdaQueryWrapper<TSyncAppSystemManage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TSyncAppSystemManage::getInSystemFlag, 1);
        lambdaQueryWrapper.eq(TSyncAppSystemManage::getStatus, 1);
//        lambdaQueryWrapper.eq(TSyncAppSystemManage::getIsAutoSyncUser,1);
        return tSyncAppSystemManageMapper.selectListNoAdd(lambdaQueryWrapper);
    }


    @Override
    public List<TSyncAppSystemManage> getActiveProjectsOrg() {
        LambdaQueryWrapper<TSyncAppSystemManage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TSyncAppSystemManage::getInSystemFlag, 1);
        lambdaQueryWrapper.eq(TSyncAppSystemManage::getStatus, 1);
//        lambdaQueryWrapper.eq(TSyncAppSystemManage::getIsAutoSyncOrg,1);
        return tSyncAppSystemManageMapper.selectListNoAdd(lambdaQueryWrapper);
    }

    @Override
    public void ownedOrgValid(String inOwnedOrgId, SyncOrgUserDTO syncOrgUserDTO) {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (null == cscpUserDetail) {
            cscpUserDetail = syncOrgUserDTO.getCscpUserDetail();
        }
        CscpUser cscpUser = cscpUserService.getById(cscpUserDetail.getId());
        if (StringUtils.isEmpty(inOwnedOrgId)
                || SecurityUtils.isAdmin() || "aqbmgly".equals(cscpUser.getLoginName())) {
            // 应用无绑定单位 | admin，不做校验
            return;
        }

        if (syncOrgUserDTO.getOrgId() != null) {
            if (!validateOrg(inOwnedOrgId, syncOrgUserDTO.getOrgId())) {
                // 如果没有匹配的机构，抛出异常
                throw new BusinessException("推送机构与应用绑定市无关联关系！(inOwnedOrgId=" + inOwnedOrgId + ", orgId=" + syncOrgUserDTO.getOrgId() + ")");
            }
        } else if (syncOrgUserDTO.getUserId() != null) {
            if (!validateUserOrgs(inOwnedOrgId, syncOrgUserDTO.getUserId())) {
                throw new BusinessException("推送用户与应用绑定市无关联关系！");
            }
        }
    }

    /**
     * 校验推送机构与应用绑定市关联关系
     * @param inOwnedOrgId
     * @param orgId
     */
    private boolean validateOrg(String inOwnedOrgId, Long orgId) {
        // 输入校验
        if (inOwnedOrgId == null || inOwnedOrgId.isEmpty()) {
            throw new IllegalArgumentException("参数 inOwnedOrgId 不能为空");
        }
        if (orgId == null) {
            throw new IllegalArgumentException("参数 orgId 不能为空");
        }

        // 获取机构树(其中一定有市)
        List<CscpOrg> cscpOrgs = cscpOrgRepository.selectSelf2RootOrg(orgId);
        if (CollectionUtils.isEmpty(cscpOrgs)) {
            throw new BusinessException("未找到与 orgId 关联的机构信息");
        }

        // 将 inOwnedOrgId 转换为集合
        List<String> ownedOrgIds = Arrays.asList(inOwnedOrgId.split(","));
        if (ownedOrgIds.isEmpty()) {
            throw new BusinessException("inOwnedOrgId 不应为空列表");
        }

        // 提前将 cscpOrgs 的 ID 转换为 Set，提升查找效率
        Set<String> orgIdSet = cscpOrgs.stream()
                .map(cscpOrg -> cscpOrg.getId().toString())
                .collect(Collectors.toSet());

        // 验证是否存在匹配的机构
        for (String ownedOrgId : ownedOrgIds) {
            if (orgIdSet.contains(ownedOrgId)) {
                return true; // 找到匹配的机构（市），直接返回
            }
        }

        return false;
    }


    /**
     * 校验推送用户与应用绑定单位关联关系
     * @param inOwnedOrgId 绑定单位ID
     * @param userId 用户ID
     */
    private boolean validateUserOrgs(String inOwnedOrgId, Long userId) {
        LambdaQueryWrapper<CscpUserOrg> userOrgQW = Wrappers.lambdaQuery();
        userOrgQW.eq(CscpUserOrg::getUserId, userId);
        userOrgQW.orderByAsc(CscpUserOrg::getOrderBy);
        List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(userOrgQW);
        String[] list = inOwnedOrgId.split(",");
        for (String ownedOrgId : list) {
            for (CscpUserOrg cscpUserOrg : cscpUserOrgs) {
                if (validateOrg(ownedOrgId, cscpUserOrg.getOrgId())) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * 获取推送应用管理列表
     * admin用户获取全部
     * 其他用户获取当前所在机构编码所绑定应用
     * @return
     */
    public List<TSyncAppSystemManage> existAppSystemManage() {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        String loginUserName = SecurityUtils.getCurrentUserName();
        // 检查应用绑定配置
        AppBindingCheckResult bindingResult = checkAppBindingConfig(loginUserName);
        // 如果当前用户匹配到绑定配置，直接返回
        if (bindingResult.isDirectReturn()) {
            return bindingResult.getAppData();
        }

        // 系统管理员、安全管理员、平台管理员
        if (SecurityUtils.isAdmin()
                || "aqbmgly".equals(loginUserName)
                || SecurityUtils.isSystemName()) {
            return tSyncAppSystemManageMapper.selectListNoAdd(Wrappers.lambdaQuery(TSyncAppSystemManage.class)
                    .eq(TSyncAppSystemManage::getStatus, 1)
                    .ne(StrUtil.isNotBlank(bindingResult.getBindingAppName()), TSyncAppSystemManage::getAppName, bindingResult.getBindingAppName())
                    .in(CollUtil.isNotEmpty(bindingResult.getBindingAppIdList()), TSyncAppSystemManage::getId, bindingResult.getBindingAppIdList())
                    .orderByDesc(TSyncAppSystemManage::getCreateTime));
        }
        // 租户管理员
        if (SecurityUtils.isTenantName()) {
            return tSyncAppSystemManageMapper.selectListNoAdd(Wrappers.lambdaQuery(TSyncAppSystemManage.class)
                    .eq(TSyncAppSystemManage::getTenantId, cscpUserDetail.getTenantId())
                    .eq(TSyncAppSystemManage::getStatus, 1)
                    .ne(StrUtil.isNotBlank(bindingResult.getBindingAppName()), TSyncAppSystemManage::getAppName, bindingResult.getBindingAppName())
                    .in(CollUtil.isNotEmpty(bindingResult.getBindingAppIdList()), TSyncAppSystemManage::getId, bindingResult.getBindingAppIdList())
                    .orderByDesc(TSyncAppSystemManage::getCreateTime));
        }
        // 区域机构管理员
        if (SecurityUtils.isRegionAdmin()) {
            LambdaQueryWrapper<TSyncAppSystemManage> appLqw = Wrappers.lambdaQuery();
//            appLqw.in(TSyncAppSystemManage::getInOwnedOrgId, cscpUserDetail.getCompanyId(), cscpUserDetail.getDepartmentId());
            appLqw.eq(TSyncAppSystemManage::getStatus, 1)
                    .ne(StrUtil.isNotBlank(bindingResult.getBindingAppName()), TSyncAppSystemManage::getAppName, bindingResult.getBindingAppName())
                    .in(CollUtil.isNotEmpty(bindingResult.getBindingAppIdList()), TSyncAppSystemManage::getId, bindingResult.getBindingAppIdList())
                    .orderByDesc(TSyncAppSystemManage::getCreateTime);
            return tSyncAppSystemManageMapper.selectListNoAdd(appLqw);
        }
        // 部门（内设机构）管理员
        if (SecurityUtils.isDepartAdmin()) {
            LambdaQueryWrapper<TSyncAppSystemManage> appLqw = Wrappers.lambdaQuery();
            appLqw.eq(TSyncAppSystemManage::getStatus, 1)
                    .ne(StrUtil.isNotBlank(bindingResult.getBindingAppName()), TSyncAppSystemManage::getAppName, bindingResult.getBindingAppName())
                    .in(CollUtil.isNotEmpty(bindingResult.getBindingAppIdList()), TSyncAppSystemManage::getId, bindingResult.getBindingAppIdList())
                    .orderByDesc(TSyncAppSystemManage::getCreateTime);
            return tSyncAppSystemManageMapper.selectListNoAdd(appLqw);
        }

        // 单位管理员
        if (SecurityUtils.isUnitAdmin()) {
            // 获取当前登录用户的机构
            /*if (null == cscpUserDetail.getCompanyId()) {
                throw new BusinessException("用户单位id不存在!");
            }
            LambdaQueryWrapper<CscpOrg> orgLqw = Wrappers.lambdaQuery();
            orgLqw.eq(CscpOrg::getId, cscpUserDetail.getCompanyId());
            orgLqw.eq(CscpOrg::getType, 2);
            List<CscpOrg> cscpOrg = cscpOrgService.selectListNoAdd(orgLqw);
            List<Long> orgIds = cscpOrg.stream().map(CscpOrg::getId).collect(Collectors.toList());
            if (orgIds.isEmpty()) {
                return Collections.emptyList();
            }
            Long currOrgId = orgIds.get(0);
            // 获取机构树(其中一定有市)
            List<CscpOrg> cscpOrgs = cscpOrgRepository.selectSelf2RootOrg(currOrgId);
            // 得到市ID
            Set<Long> cityOrgId = new HashSet<>();
            for (CscpOrg org : cscpOrgs) {
                if (!"4300".equals(org.getOrgCode().substring(0,4))) {
                    if (org.getPathCode().length() == 12) {
                        cityOrgId.add(org.getId());
                    }
                }
            }*/

            // 匹配当前用户对应的推送应用
            LambdaQueryWrapper<TSyncAppSystemManage> appLqw = Wrappers.lambdaQuery();
//            appLqw.in(CollectionUtils.isNotEmpty(cityOrgId),TSyncAppSystemManage::getInOwnedOrgId, cityOrgId);
            appLqw.eq(TSyncAppSystemManage::getStatus, 1)
                    .ne(StrUtil.isNotBlank(bindingResult.getBindingAppName()), TSyncAppSystemManage::getAppName, bindingResult.getBindingAppName())
                    .orderByDesc(TSyncAppSystemManage::getCreateTime);
            return tSyncAppSystemManageMapper.selectListNoAdd(appLqw);
        }
        return Collections.emptyList();
    }

    public void existUserPush(SyncOrgUserDTO syncOrgUserDTO, CscpUser cscpUser) {
        if (StringUtils.endsWith(cscpUser.getLoginName(), "admin")) {
            // 单位管理员不需要往下了
            return;
        }

        LambdaQueryWrapper<CscpUserOrg> userOrgLqw = Wrappers.lambdaQuery();
        userOrgLqw.eq(CscpUserOrg::getUserId, cscpUser.getId());
        List<CscpUserOrg> userOrgList = cscpUserOrgService.selectListNoAdd(userOrgLqw);

        List<Long> orgIdList = userOrgList.stream().map(CscpUserOrg::getOrgId).collect(Collectors.toList());

        syncOrgUserDTO.setOrgIds(orgIdList);
        this.mqSyncUserOfOrg(syncOrgUserDTO);
    }

    /**
     * 推送用户记录
     *
     * @param appSystemManage
     * @param cscpUserDTO
     * @param strOperaType
     * @param cscpUserDetail
     * @return
     */
    @SuppressWarnings("all")
    private TSyncUserHistroyRecord setUserHistory(TSyncAppSystemManage appSystemManage,
                                                  CscpUserDTO cscpUserDTO,
                                                  String strOperaType, CscpUserDetail cscpUserDetail2) {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (cscpUserDetail == null) {
            cscpUserDetail = cscpUserDetail2;
        }
        TSyncUserHistroyRecord userRecord = new TSyncUserHistroyRecord();
        userRecord.setAppId(appSystemManage.getId());
        userRecord.setUserId(cscpUserDTO.getId());
        userRecord.setStrCname(cscpUserDTO.getRealName());
        userRecord.setStrUserId(cscpUserDTO.getLoginName());
        userRecord.setLoginName(cscpUserDTO.getLoginName());
        userRecord.setStrId(cscpUserDTO.getStrId());
        userRecord.setStrOperaType(strOperaType);
        userRecord.setRequestMode(StringUtils.isEmpty(appSystemManage.getRequestMode())
                ? OrgUserConstants.RequestMode.REQUEST_MODE_HTTP : appSystemManage.getRequestMode());
        userRecord.setCreateBy(cscpUserDetail.getId());
        userRecord.setCreateName(cscpUserDetail.getRealName());
        userRecord.setCreateTime(DateUtils.getLocalDateTime());
        userRecord.setDepartmentId(null != cscpUserDetail.getDepartmentId() ? cscpUserDetail.getDepartmentId() : null);
        userRecord.setCompanyId(null != cscpUserDetail.getCompanyId() ? cscpUserDetail.getCompanyId() : null);
        userRecord.setTenantId(null != cscpUserDetail.getTenantId() ? cscpUserDetail.getTenantId() : null);
        userRecord.setInSystemFlag(appSystemManage.getInSystemFlag());
        userRecord.setStrMobile(cscpUserDTO.getMobile());
        // 同步中
        userRecord.setSyncSuccess("ing");
        userRecord.setSyncMessage("推送中");
        userRecord.setSyncStatus("205");
        return userRecord;
    }

    /**
     * 推送机构记录
     *
     * @param appSystemManage
     * @param cscpOrg
     * @param strOperaType
     * @param cscpUserDetail
     * @return
     */
    @SuppressWarnings("all")
    private TSyncOrgHistroyRecord setOrgHistory(TSyncAppSystemManage appSystemManage,
                                                CscpOrg cscpOrg,
                                                String strOperaType, CscpUserDetail cscpUserDetail2) {
        CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        if (cscpUserDetail == null) {
            cscpUserDetail = cscpUserDetail2;
        }
        TSyncOrgHistroyRecord orgRecord = new TSyncOrgHistroyRecord();
        orgRecord.setAppId(appSystemManage.getId());
        orgRecord.setOrgId(cscpOrg.getId());
        orgRecord.setParentId(cscpOrg.getParentId());
        orgRecord.setStrUnitName(cscpOrg.getOrgName());
        orgRecord.setStrUnitCode(cscpOrg.getOrgCode());
        orgRecord.setStrAreaCode(cscpOrg.getRegionCode());
        orgRecord.setStrOperaType(strOperaType);
        orgRecord.setStrId(cscpOrg.getStrId());
        orgRecord.setStrParentId(cscpOrg.getStrParentId());
        orgRecord.setRequestMode(StringUtils.isEmpty(appSystemManage.getRequestMode())
                ? OrgUserConstants.RequestMode.REQUEST_MODE_HTTP : appSystemManage.getRequestMode());
        orgRecord.setCreateBy(cscpUserDetail.getId());
        orgRecord.setCreateName(cscpUserDetail.getRealName());
        orgRecord.setCreateTime(DateUtils.getLocalDateTime());
        orgRecord.setDepartmentId(null != cscpUserDetail.getDepartmentId() ? cscpUserDetail.getDepartmentId() : null);
        orgRecord.setCompanyId(null != cscpUserDetail.getCompanyId() ? cscpUserDetail.getCompanyId() : null);
        orgRecord.setTenantId(null != cscpUserDetail.getTenantId() ? cscpUserDetail.getTenantId() : null);
        orgRecord.setInSystemFlag(appSystemManage.getInSystemFlag());
        // 同步中
        orgRecord.setSyncSuccess("ing");
        orgRecord.setSyncMessage("推送中");
        orgRecord.setSyncStatus("205");
        return orgRecord;
    }

    private boolean checkSyncOrg(TSyncAppSystemManage appSystemManage, CscpOrg cscpOrg) {
        // 如果应用绑定机构,判断推送的机构和绑定的机构是否包含;
        // 包含推送，反之不需自动推送
        if (StringUtils.isNotEmpty(appSystemManage.getInOwnedOrgId())) {
            String appOrgId = appSystemManage.getInOwnedOrgId();
            List<Long> orgIds = cscpOrgRepository.selectSelf2RootOrg(cscpOrg.getId())
                    .stream().map(CscpOrg::getId).collect(Collectors.toList());
            return orgIds.contains(Long.valueOf(appOrgId));
        }
        return true;
    }

    private boolean checkSyncUser(TSyncAppSystemManage appSystemManage, CscpUserDTO cscpUserDTO) {
        List<CscpUserOrg> newOrgList = new ArrayList<>();
        // 应用没有绑定市州,则直接返回
        if (StringUtils.isEmpty(appSystemManage.getInOwnedOrgId())) {
            return true;
        }
        // 用户所属机构是否跟应用绑定市州是否存在关系
        if (CollectionUtils.isNotEmpty(cscpUserDTO.getOrgList())) {
            log.info("checkSyncUser方法>>>>用户筛选前推送机构：{}", JSONObject.toJSONString(newOrgList));
            List<CscpUserOrg> oldOrgList = cscpUserDTO.getOrgList();
            // 筛选出用户可推送机构
            for (CscpUserOrg cscpUserOrg : oldOrgList) {
                String appOrgId = appSystemManage.getInOwnedOrgId();
                List<Long> orgIds = cscpOrgRepository.selectSelf2RootOrg(cscpUserOrg.getOrgId())
                        .stream().map(CscpOrg::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(orgIds)) {
                    if (orgIds.contains(Long.valueOf(appOrgId))) {
                        newOrgList.add(cscpUserOrg);
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(newOrgList)) {
                log.info("checkSyncUser方法>>>>用户筛选后推送机构：{}", JSONObject.toJSONString(newOrgList));
                cscpUserDTO.setOrgList(newOrgList);
                return true;
            }
        }
        return false;
    }

    /**
     * 根据应用id和机构id,查询是否存在成功的推送记录
     * @param appSystemManage
     * @param orgId
     * @return
     */
    @SuppressWarnings("all")
    @Override
    public String setOrgStrOperaType(TSyncAppSystemManage appSystemManage, Long orgId) {
        String strOperaType;
        LambdaQueryWrapper<TSyncOrgHistroyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSyncOrgHistroyRecord::getAppId, appSystemManage.getId());
        queryWrapper.eq(TSyncOrgHistroyRecord::getOrgId, orgId);
        queryWrapper.eq(TSyncOrgHistroyRecord::getSyncSuccess, "true");
        queryWrapper.eq(TSyncOrgHistroyRecord::getSyncStatus, "200");
        queryWrapper.ne(TSyncOrgHistroyRecord::getStrOperaType, "delete");
        queryWrapper.eq(TSyncOrgHistroyRecord::getInSystemFlag, appSystemManage.getInSystemFlag());
        List<TSyncOrgHistroyRecord> orgHistroyRecords = tSyncOrgHistroyRecordMapper.selectListNoAdd(queryWrapper);
        if (orgHistroyRecords == null || orgHistroyRecords.isEmpty()) {
            //推送用户新增
            strOperaType = "add";
        } else {
            //推送用户更新
            strOperaType = "update";
        }
        return strOperaType;
    }

    /**
     * 根据应用id和用户id,查询是否存在成功的推送记录
     * @param appSystemManage
     * @param userId
     * @return
     */
    @Override
    @SuppressWarnings("all")
    public String setUserStrOperaType(TSyncAppSystemManage appSystemManage, Long userId) {
        String strOperaType;
        LambdaQueryWrapper<TSyncUserHistroyRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSyncUserHistroyRecord::getAppId, appSystemManage.getId());
        queryWrapper.eq(TSyncUserHistroyRecord::getUserId, userId);
        queryWrapper.eq(TSyncUserHistroyRecord::getSyncSuccess, "true");
        queryWrapper.eq(TSyncUserHistroyRecord::getSyncStatus, "200");
        queryWrapper.ne(TSyncUserHistroyRecord::getStrOperaType, "delete");
        queryWrapper.eq(TSyncUserHistroyRecord::getInSystemFlag, appSystemManage.getInSystemFlag());
        List<TSyncUserHistroyRecord> userHistroyRecords = tSyncUserHistroyRecordMapper.selectListNoAdd(queryWrapper);
        if (userHistroyRecords == null || userHistroyRecords.isEmpty()) {
            //推送用户新增
            strOperaType = "add";
        } else {
            //推送用户更新
            strOperaType = "update";
        }
        return strOperaType;
    }

    @Override
    @SuppressWarnings("all")
    public void commonHttpRequest(SyncCommonHttpDTO dto, boolean isMq) {
        TSyncAppSystemManage appSystemManage = dto.getAppSystemManage();
        Map<String, Object> bodyParams = dto.getBodyParams();
        SyncOrgUserDTO syncOrgUserDTO = dto.getSyncOrgUserDTO();
        Integer inSystemFlag = appSystemManage.getInSystemFlag();
        String pushName = SyncAppSystemEnum.getNameByCode(inSystemFlag);
        SyncModel syncModel = null;
        try {
            syncModel = (SyncModel) bodyParams.get("mode");
        } catch (Exception e) {
            syncModel = JSONObject.parseObject(bodyParams.get("mode").toString(), SyncModel.class);
        }
        String strCodeType = syncModel.getStrCodeType();
        if (!isMq && OrgUserConstants.RequestMode.REQUEST_MODE_PULL_HTTP.equals(appSystemManage.getRequestMode())) {
            if ("PUSH_USER".equals(strCodeType)) {
                PassiveSyncMessage message = new PassiveSyncMessage();
                message.setAppSystemId(appSystemManage.getId());
                message.setType(1);
                message.setUserId(dto.getCscpUserDto().getId());
                // 插入推送记录
                TSyncUserHistroyRecord record = this.insertUserSyncRecord(dto.getCscpUserDto(), dto.getAppSystemManage(), dto.getCscpUserDetail());
                message.setSyncHistoryId(record.getId());
                eventPublisher.publishEvent(message);
                return;
            } else if ("PUSH_UNIT".equals(strCodeType)) {
                PassiveSyncMessage message = new PassiveSyncMessage();
                message.setAppSystemId(appSystemManage.getId());
                message.setType(0);
                message.setOrgId(dto.getCscpOrg().getId());
                // 插入推送记录
                TSyncOrgHistroyRecordDTO record = itSyncOrgHistroyRecordService.
                    generateOrgHistoryRecord(dto.getCscpOrg().getId(), dto.getAppSystemManage(), dto.getCscpUserDetail());
                message.setSyncHistoryId(record.getId());
                eventPublisher.publishEvent(message);
                // 单位管理员
                uintAdminPush(dto, syncModel.getStrOperaType(), appSystemManage);
                return;
            }
        }

        // add mq 方式
        String flag = sysConfigService.getSysConfigValueByCode("sync:mqToHttp");
        if (!isMq && "1".equals(flag) && !(SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag && "PUSH_UNIT".equals(strCodeType))) {
            if (HttpPushUserConsumer.LZ_ROUTING_KEY.equals(appSystemManage.getAppCode()) && "PUSH_USER".equals(strCodeType)) {
                HttpPushUserDTO httpPushUserDTO = new HttpPushUserDTO();
                httpPushUserDTO.setAppSystemManage(appSystemManage);
                httpPushUserDTO.setUserId(dto.getCscpUserDto().getId());
                httpPushUserDTO.setCscpUserDetail(ObjectUtils.defaultIfNull(SecurityUtils.getCurrentCscpUserDetail(), dto.getCscpUserDetail()));
                httpPushUserDTO.setFlag(dto.getSyncOrgUserDTO().getFlag());
                httpPushUserDTO.setIsAutoPushFlag(dto.getSyncOrgUserDTO().getIsAutoPushFlag());
                // 推送到量子特殊队列定时延迟消费
                httpPushUserProducer.httpPushUserToLzQueue(JSONObject.toJSONString(httpPushUserDTO));
                return;
            }
            dto.setUuid(UUID.randomUUID().toString());
            rabbitTemplate.convertAndSend(SyncCommonHttpConsumer.EXCHANGE, SyncCommonHttpConsumer.ROUTING_KEY, JSONObject.toJSONString(dto));
            return;
        }

        if (!isMq && "1".equals(flag) && SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag && "PUSH_UNIT".equals(strCodeType)) {
            HttpPushOrgDTO httpPushOrgDTO = new HttpPushOrgDTO();
            httpPushOrgDTO.setAppSystemManage(appSystemManage);
            httpPushOrgDTO.setOrgId(dto.getCscpOrg().getId());
            httpPushOrgDTO.setCscpUserDetail(ObjectUtils.defaultIfNull(SecurityUtils.getCurrentCscpUserDetail(), dto.getCscpUserDetail()));
            httpPushOrgDTO.setFlag(dto.getSyncOrgUserDTO().getFlag());
            httpPushOrgDTO.setIsAutoPushFlag(dto.getSyncOrgUserDTO().getIsAutoPushFlag());
            // 外部机构应用推送,MQ单机顺序消费
            httpPushOrgProducer.httpPushOrgToQueueWaiBu(JSONObject.toJSONString(httpPushOrgDTO));
            return;
        }


        Pair<StatusLine, String> resp = null;
        String url = "";
        if (SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag) {
            // 外部应用拿syncUrl
            url = appSystemManage.getSyncUrl();
        } else {
            // 内部应用拿inSystemUrl
            url = appSystemManage.getInSystemUrl();
        }
        // 判断请求地址是否合法
        boolean urlFlag = isValidHttpUrl(url);
        if (!urlFlag) {
            return;
        }
        SyncResponseModel responseModel = new SyncResponseModel();
        SyncResponseModelError responseModelError = new SyncResponseModelError();
        try {
            log.info("commonHttpRequest bodyParams: " + JSONObject.toJSONString(bodyParams));
            resp = WpsUtil.sendPostRequestConfigTimeout(url, null, bodyParams, 10000, 10000);
            log.info("commonHttpRequest resp: " + JSONObject.toJSONString(resp));
            JSONObject jsonObject = JSONObject.parseObject(resp.getValue());
            responseModel = JSONObject.toJavaObject(jsonObject, SyncResponseModel.class);
        } catch (Exception e) {
            String msg = StringUtils.substring(e.getMessage(), 0, 200);
            log.error("{},触发HTTP请求异常: {}", appSystemManage.getAppName(), e.getMessage());
            if (e instanceof HttpHostConnectException) {
                responseModelError.setErrorMsg("服务器连接失败，请检查URL、IP或端口是否正确!" + msg);
            } else if (e instanceof ClientProtocolException) {
                responseModelError.setErrorMsg("协议处理错误，可能是请求格式或响应解析失败!" + msg);
            } else if (e instanceof SocketTimeoutException) {
                responseModelError.setErrorMsg("请求超时，请尝试增加超时时间或检查网络稳定性!" + msg);
            } else if (e instanceof IOException) {
                responseModelError.setErrorMsg("网络IO错误，可能是连接中断或数据读取失败!" + msg);
            } else if (e instanceof Exception) {
                responseModelError.setErrorMsg("未知异常，请求处理失败!" + msg);
            }
        } finally {
            // 机构
            if ("PUSH_UNIT".equals(strCodeType)) {
                // 获取机构数据
                CscpOrg cscpOrg = dto.getCscpOrg();
                TSyncOrgHistroyRecord insertRecord = new TSyncOrgHistroyRecord();
                // 如果是外部应用
                if (SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag) {
                    // 走迁移过来的赋值操作
                    insertRecord.setAppId(appSystemManage.getId());
                    insertRecord.setOrgId(cscpOrg.getId());
                    insertRecord.setStrId(cscpOrg.getStrId());
                    insertRecord.setStrParentId(cscpOrg.getStrParentId());
                    insertRecord.setParentId(cscpOrg.getParentId());
                    insertRecord.setStrUnitName(cscpOrg.getOrgName());
                    insertRecord.setStrUnitCode(cscpOrg.getOrgCode());
                    insertRecord.setStrAreaCode(cscpOrg.getRegionCode());
                    insertRecord.setStrOperaType(syncModel.getStrOperaType());
                    insertRecord.setRequestMode(StringUtils.isEmpty(appSystemManage.getRequestMode())
                            ? OrgUserConstants.RequestMode.REQUEST_MODE_HTTP : appSystemManage.getRequestMode());
                    insertRecord.setInSystemFlag(inSystemFlag);
                    insertRecord.setSourceId(syncOrgUserDTO.getSourceId());
                } else {
                    // 默认的赋值操作
                    insertRecord = this.setOrgHistory(appSystemManage, cscpOrg, syncModel.getStrOperaType(), dto.getCscpUserDetail());
                }
                // 以下始终会执行
                httpOrgResultHandle(insertRecord, resp, responseModel, responseModelError);

                log.info("{}应用: {}, 执行保存机构推送记录操作", pushName, appSystemManage.getAppName());
                // 手动设置create_time，确保分表路由正确
                if (insertRecord.getCreateTime() == null) {
                    insertRecord.setCreateTime(LocalDateTime.now());
                }
                tSyncOrgHistroyRecordMapper.insert(insertRecord);
                // 内部请求才会调用
                if (SyncAppSystemEnum.NEI_BU.getCode() == inSystemFlag) {
                    // 内部系统调用重推机制
                    retrySend(appSystemManage.getInSystemUrl(), bodyParams, insertRecord);
                }
                // 机构创建成功才会推送单位管理员
                if (syncModel.getStrOperaType().equals("add") && isR(resp, responseModel)) {
                    // 如果推送单位，需要同时推送单位管理员账号
                    if (ObjectUtil.equals(cscpOrg.getType(), 2)) {
                        // 1 走迁移的内部单位管理员推送
                        // 0 走迁移的外部单位管理员推送
                        if (1 == inSystemFlag) {
                            SyncOrgUserDTO userDTO = new SyncOrgUserDTO();
                            userDTO.setCscpUserDetail(dto.getCscpUserDetail());
                            userDTO.setAppId(appSystemManage.getId());
                            // 根据单位id查询单位管理员
                            LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper<>();
                            userOrgQW.eq(CscpUserOrg::getOrgId, cscpOrg.getId());
                            userOrgQW.orderByAsc(CscpUserOrg::getUserId);
                            userOrgQW.apply("org_id = company_id");
                            userOrgQW.last("limit 1");
                            CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(userOrgQW);
                            log.info("导入单位推送单位管理员:" + JSONObject.toJSON(cscpUserOrg));
                            if (cscpUserOrg != null) {
                                userDTO.setUserId(cscpUserOrg.getUserId());
                                userDTO.setFlag(syncModel.getStrOperaType());
                                this.syncUserInSystem(userDTO);
                            }
                        } else {
                            SyncOrgUserDTO userDTO = new SyncOrgUserDTO();
                            userDTO.setCscpUserDetail(dto.getCscpUserDetail());
                            userDTO.setIsAutoPushFlag(syncOrgUserDTO.getIsAutoPushFlag());
                            userDTO.setFlag(syncOrgUserDTO.getFlag());
                            userDTO.setAppId(syncOrgUserDTO.getAppId());
                            // 根据单位id查询单位管理员
                            LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper();
                            // 这里有问题 外面有地方syncOrgUserDTO.setOrgId(cscpOrg.getId());
                            userOrgQW.eq(CscpUserOrg::getOrgId, cscpOrg.getId());
                            userOrgQW.orderByAsc(CscpUserOrg::getUserId);
                            userOrgQW.apply("org_id = company_id");
                            userOrgQW.last("limit 1");
                            CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(userOrgQW);
                            log.info("导入单位推送单位管理员:" + JSONObject.toJSON(cscpUserOrg));
                            if (cscpUserOrg != null) {
                                userDTO.setUserId(cscpUserOrg.getUserId());
                                userDTO.setUnitAdminFlag("1");
                                this.syncUser(userDTO);
                            }
                        }

                    }
                }
            } else if ("PUSH_USER".equals(strCodeType)) {
                TSyncUserHistroyRecord insertRecord = new TSyncUserHistroyRecord();
                // 如果是外部推送
                if (SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag) {
                    // 获取用户信息
                    SyncOrgUserModel syncOrgUserModel = null;
                    try {
                        syncOrgUserModel = (SyncOrgUserModel) bodyParams.get("dataInfo");
                    } catch (Exception e) {
                        syncOrgUserModel = JSONObject.parseObject(bodyParams.get("dataInfo").toString(), SyncOrgUserModel.class);
                    }
                    insertRecord = BeanConvertUtils.copyProperties(syncOrgUserModel, TSyncUserHistroyRecord.class);
                    insertRecord.setAppId(appSystemManage.getId());
                    insertRecord.setUserId(syncOrgUserDTO.getUserId());
                    insertRecord.setLoginName(syncOrgUserDTO.getLoginName());
                    insertRecord.setStrOperaType(syncModel.getStrOperaType());
                    insertRecord.setInSystemFlag(inSystemFlag);
                    insertRecord.setRequestMode(StringUtils.isEmpty(appSystemManage.getRequestMode())
                            ? OrgUserConstants.RequestMode.REQUEST_MODE_HTTP : appSystemManage.getRequestMode());
                } else {
                    // 获取用户信息
                    CscpUserDTO cscpUserDTO = dto.getCscpUserDto();
                    insertRecord = this.setUserHistory(appSystemManage, cscpUserDTO, syncModel.getStrOperaType(), dto.getCscpUserDetail());
                }
                //插入推送记录表
                httpUserResultHandle(insertRecord, resp, responseModel, responseModelError);

                log.info("{}应用: {}, 执行保存用户推送记录操作", pushName, appSystemManage.getAppName());
                // 手动设置create_time，确保分表路由正确
                if (insertRecord.getCreateTime() == null) {
                    insertRecord.setCreateTime(LocalDateTime.now());
                }
                tSyncUserHistroyRecordMapper.insert(insertRecord);
                // 内部请求才会调用
                if (SyncAppSystemEnum.NEI_BU.getCode() == inSystemFlag) {
                    // 内部系统调用重推机制
                    retrySend(appSystemManage.getInSystemUrl(), bodyParams, insertRecord);
                }
            }
        }
    }

    public void uintAdminPush(SyncCommonHttpDTO dto, String strOperaType, TSyncAppSystemManage appSystemManage) {
        if (("add".equals(dto.getSyncOrgUserDTO().getFlag()) || "add".equals(strOperaType)) && ObjectUtil.equals(dto.getCscpOrg().getType(), 2)) {
            // 根据单位id查询单位管理员
            LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper<>();
            userOrgQW.eq(CscpUserOrg::getOrgId, dto.getCscpOrg().getId());
            userOrgQW.apply("org_id = company_id");
            userOrgQW.last("limit 1");
            CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(userOrgQW);
            CscpUser cscpUser = cscpUserRepository.selectById(cscpUserOrg.getUserId());
            CscpUserDTO cscpUserDTO = new CscpUserDTO();
            BeanUtils.copyProperties(cscpUser, cscpUserDTO);
            PassiveSyncMessage passiveSyncMessage = new PassiveSyncMessage();
            passiveSyncMessage.setAppSystemId(appSystemManage.getId());
            passiveSyncMessage.setUserId(cscpUserOrg.getUserId());
            // 推送用户
            passiveSyncMessage.setType(OrgUserConstants.PushType.PUSH_USER);
            // 插入推送记录
            TSyncUserHistroyRecord userRecord = this.insertUserSyncRecord(cscpUserDTO, appSystemManage, dto.getCscpUserDetail());
            passiveSyncMessage.setSyncHistoryId(userRecord.getId());
            eventPublisher.publishEvent(passiveSyncMessage);
        }
    }

    private void httpUserResultHandle(TSyncUserHistroyRecord insertRecord,
                                  Pair<StatusLine, String> resp,
                                  SyncResponseModel responseModel,
                                  SyncResponseModelError responseModelError) {
        if (isR(resp, responseModel)) {
            insertRecord.setSyncSuccess("true");
            insertRecord.setSyncMessage(responseModel.getMessage());
            insertRecord.setSyncStatus(null != responseModel.getObj() ? responseModel.getObj().getStatus() : "200");
        } else if (StringUtils.isNotEmpty(responseModelError.getErrorMsg())) {
            insertRecord.setSyncSuccess(responseModelError.getSuccess().toString());
            insertRecord.setSyncMessage(responseModelError.getErrorMsg());
            insertRecord.setSyncStatus(responseModelError.getStatusCode());
        } else {
            if (responseModel.getSuccess() != null) {
                insertRecord.setSyncSuccess(responseModel.getSuccess().toString());
            } else {
                insertRecord.setSyncSuccess("false");
            }
            insertRecord.setSyncMessage(StringUtils.defaultString(responseModel.getMessage(), resp.toString()));
            if (responseModel.getObj() != null && responseModel.getObj().getStatus() != null) {
                insertRecord.setSyncStatus(responseModel.getObj().getStatus());
            } else {
                insertRecord.setSyncStatus("5004");
            }
        }
    }

    private void httpOrgResultHandle(TSyncOrgHistroyRecord insertRecord,
                                      Pair<StatusLine, String> resp,
                                      SyncResponseModel responseModel,
                                      SyncResponseModelError responseModelError) {
        if (isR(resp, responseModel)) {
            insertRecord.setSyncSuccess("true");
            insertRecord.setSyncMessage(responseModel.getMessage());
            insertRecord.setSyncStatus(null != responseModel.getObj() ? responseModel.getObj().getStatus() : "200");
        } else if (StringUtils.isNotEmpty(responseModelError.getErrorMsg())) {
            insertRecord.setSyncSuccess(responseModelError.getSuccess().toString());
            insertRecord.setSyncMessage(responseModelError.getErrorMsg());
            insertRecord.setSyncStatus(responseModelError.getStatusCode());
        } else {
            if (responseModel.getSuccess() != null) {
                insertRecord.setSyncSuccess(responseModel.getSuccess().toString());
            } else {
                insertRecord.setSyncSuccess("false");
            }
            insertRecord.setSyncMessage(StringUtils.defaultString(responseModel.getMessage(), resp.toString()));
            if (responseModel.getObj() != null && responseModel.getObj().getStatus() != null) {
                insertRecord.setSyncStatus(responseModel.getObj().getStatus());
            } else {
                insertRecord.setSyncStatus("5004");
            }
        }
    }

    public boolean commonHttpRequestNew(SyncCommonHttpDTO dto) {
        try {
            TSyncAppSystemManage appSystemManage = dto.getAppSystemManage();
            Map<String, Object> bodyParams = dto.getBodyParams();

            // 1. 解析 SyncModel，安全转换类型
            SyncModel syncModel = parseBodyParam(bodyParams, "mode", SyncModel.class);
            if (syncModel == null) {
                log.warn("无法解析 bodyParams 中的 SyncModel");
                return false;
            }

            Integer inSystemFlag = appSystemManage.getInSystemFlag();
            String url = SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag ? appSystemManage.getSyncUrl() : appSystemManage.getInSystemUrl();

            // 2. URL 校验
            if (!isValidHttpUrl(url)) {
                log.warn("无效URL: {}", url);
                return false;
            }
            log.debug("合法地址: {}", url);

            // 4. 发起 HTTP 请求
            Pair<StatusLine, String> resp = null;
            SyncResponseModel responseModel = new SyncResponseModel();
            SyncResponseModelError responseModelError = new SyncResponseModelError();

            try {
                log.info("commonHttpRequestNew bodyParams: " + JSONObject.toJSONString(bodyParams));
                resp = WpsUtil.sendPostRequestConfigTimeout(url, null, bodyParams, 10000, 10000);
                log.info("commonHttpRequestNew resp: " + JSONObject.toJSONString(resp));
                if (resp.getValue() != null) {
                    JSONObject jsonObject = JSONObject.parseObject(resp.getValue());
                    responseModel = JSONObject.toJavaObject(jsonObject, SyncResponseModel.class);
                }
            } catch (Exception e) {
                responseModelError = handlePushException(e);
                log.error("{}, 触发HTTP请求异常", appSystemManage.getAppName(), e);
            }

            // 5. 处理推送结果
            String strCodeType = syncModel.getStrCodeType();
            if ("PUSH_UNIT".equals(strCodeType)) {
                handlePushUnit(dto, appSystemManage, syncModel, resp, responseModel, responseModelError, bodyParams);
            } else if ("PUSH_USER".equals(strCodeType)) {
                handlePushUser(dto, appSystemManage, syncModel, resp, responseModel, responseModelError, bodyParams);
            } else {
                log.error("未知的推送类型: {}", strCodeType);
            }
            boolean r = isR(resp, responseModel);
            if (r || (SyncAppSystemEnum.NEI_BU.getCode() == appSystemManage.getInSystemFlag()
                    && responseModel.getObj() != null && "201".equals(responseModel.getObj().getStatus()))) {
                return true;
            }
        } catch (Exception e) {
            log.error("commonHttpRequest 主流程发生异常", e);
        }
        return false;
    }

    /**
     * 安全解析 bodyParams 中的对象
     *
     * @param bodyParams 请求体参数
     * @param key        键名
     * @param clazz      目标类
     * @return 转换后的对象
     */
    private <T> T parseBodyParam(Map<String, Object> bodyParams, String key, Class<T> clazz) {
        Object value = bodyParams.get(key);
        if (value == null) {
            return null;
        }
        if (clazz.isAssignableFrom(value.getClass())) {
            return clazz.cast(value);
        }
        try {
            if (value instanceof Map) {
                return JSONObject.parseObject(JSONObject.toJSONString(value), clazz);
            } else {
                return JSONObject.parseObject(value.toString(), clazz);
            }
        } catch (Exception e) {
            log.warn("解析 {} 参数失败", key, e);
            return null;
        }
    }

    private SyncResponseModelError handlePushException(Exception e) {
        SyncResponseModelError error = new SyncResponseModelError();
        if (e instanceof HttpHostConnectException) {
            error.setErrorMsg("服务器连接失败，请检查URL、IP或端口是否正确!" + e.getMessage());
        } else if (e instanceof ClientProtocolException) {
            error.setErrorMsg("协议处理错误，可能是请求格式或响应解析失败!" + e.getMessage());
        } else if (e instanceof SocketTimeoutException) {
            error.setErrorMsg("请求超时，请尝试增加超时时间或检查网络稳定性!" + e.getMessage());
        } else if (e instanceof IOException) {
            error.setErrorMsg("网络IO错误，可能是连接中断或数据读取失败!" + e.getMessage());
        } else {
            error.setErrorMsg("未知异常，请求处理失败!" + e.getMessage());
        }
        return error;
    }

    private void handlePushUnit(SyncCommonHttpDTO dto,
                                TSyncAppSystemManage appSystemManage,
                                SyncModel syncModel,
                                Pair<StatusLine, String> resp,
                                SyncResponseModel responseModel,
                                SyncResponseModelError responseModelError, Map<String, Object> bodyParams) {
        try {
            CscpOrg cscpOrg = dto.getCscpOrg();
            TSyncOrgHistroyRecord insertRecord = this.setOrgHistory(appSystemManage, cscpOrg, syncModel.getStrOperaType(), dto.getCscpUserDetail());

            httpOrgResultHandle(insertRecord, resp, responseModel, responseModelError);

            // 手动设置create_time，确保分表路由正确
            if (insertRecord.getCreateTime() == null) {
                insertRecord.setCreateTime(LocalDateTime.now());
            }
            tSyncOrgHistroyRecordMapper.insert(insertRecord);
            if (isR(resp, responseModel)) {
                // 如果推送单位，需要同时推送单位管理员账号
                if (ObjectUtil.equals(cscpOrg.getType(), 2)) {
                    // 1 走迁移的内部单位管理员推送
                    // 0 走迁移的外部单位管理员推送
                    if (1 == appSystemManage.getInSystemFlag()) {
                        SyncOrgUserDTO userDTO = new SyncOrgUserDTO();
                        userDTO.setCscpUserDetail(dto.getCscpUserDetail());
                        userDTO.setAppId(appSystemManage.getId());
                        // 根据单位id查询单位管理员
                        LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper<>();
                        userOrgQW.eq(CscpUserOrg::getOrgId, cscpOrg.getId());
                        userOrgQW.orderByAsc(CscpUserOrg::getUserId);
                        userOrgQW.apply("org_id = company_id");
                        userOrgQW.last("limit 1");
                        CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(userOrgQW);
                        log.info("导入单位推送单位管理员:" + JSONObject.toJSON(cscpUserOrg));
                        if (cscpUserOrg != null) {
                            userDTO.setUserId(cscpUserOrg.getUserId());
                            userDTO.setFlag(syncModel.getStrOperaType());
                            this.syncUserInSystem(userDTO);
                        }
                    } else {
                        SyncOrgUserDTO syncOrgUserDTO = dto.getSyncOrgUserDTO();
                        SyncOrgUserDTO userDTO = new SyncOrgUserDTO();
                        userDTO.setCscpUserDetail(dto.getCscpUserDetail());
                        userDTO.setIsAutoPushFlag(syncOrgUserDTO.getIsAutoPushFlag());
                        userDTO.setFlag(syncOrgUserDTO.getFlag());
                        userDTO.setAppId(syncOrgUserDTO.getAppId());
                        // 根据单位id查询单位管理员
                        LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper();
                        // 这里有问题 外面有地方syncOrgUserDTO.setOrgId(cscpOrg.getId());
                        userOrgQW.eq(CscpUserOrg::getOrgId, cscpOrg.getId());
                        userOrgQW.orderByAsc(CscpUserOrg::getUserId);
                        userOrgQW.apply("org_id = company_id");
                        userOrgQW.last("limit 1");
                        CscpUserOrg cscpUserOrg = cscpUserOrgService.selectOneNoAdd(userOrgQW);
                        log.info("导入单位推送单位管理员:" + JSONObject.toJSON(cscpUserOrg));
                        if (cscpUserOrg != null) {
                            userDTO.setUserId(cscpUserOrg.getUserId());
                            userDTO.setUnitAdminFlag("1");
                            this.syncUser(userDTO);
                        }
                    }
                }
            }
            if (SyncAppSystemEnum.NEI_BU.getCode() == appSystemManage.getInSystemFlag()) {
                // 内部系统调用重推机制
                retrySend(appSystemManage.getInSystemUrl(), bodyParams, insertRecord);
            }
        } catch (Exception e) {
            log.error("处理 PUSH_UNIT 推送记录时发生异常", e);
        }
    }

    private void handlePushUser(SyncCommonHttpDTO dto,
                                TSyncAppSystemManage appSystemManage,
                                SyncModel syncModel,
                                Pair<StatusLine, String> resp,
                                SyncResponseModel responseModel,
                                SyncResponseModelError responseModelError, Map<String, Object> bodyParams) {
        try {
            CscpUserDTO cscpUserDTO = dto.getCscpUserDto();
            TSyncUserHistroyRecord insertRecord = setUserHistory(appSystemManage, cscpUserDTO, syncModel.getStrOperaType(), dto.getCscpUserDetail());

            httpUserResultHandle(insertRecord, resp, responseModel, responseModelError);

            // 手动设置create_time，确保分表路由正确
            if (insertRecord.getCreateTime() == null) {
                insertRecord.setCreateTime(LocalDateTime.now());
            }
            tSyncUserHistroyRecordMapper.insert(insertRecord);
            if (SyncAppSystemEnum.NEI_BU.getCode() == appSystemManage.getInSystemFlag()) {
                // 内部系统调用重推机制
                retrySend(appSystemManage.getInSystemUrl(), bodyParams, insertRecord);
            }
        } catch (Exception e) {
            log.error("处理 PUSH_USER 推送记录时发生异常", e);
        }
    }

    /**
     * 判断接口是否成功
     */
    private boolean isR(Pair<StatusLine, String> resp, SyncResponseModel responseModel) {
        return resp != null && resp.getKey() != null && resp.getKey().getStatusCode() == 200
                && (responseModel.getSuccess() == null || responseModel.getSuccess())
                && (responseModel.getObj() == null || "200".equals(responseModel.getObj().getStatus()));
    }

    @Override
    /**
     * 同步用户信息到多个应用系统
     *
     * @param dto 包含待同步的用户信息和目标应用系统代码的传输对象
     */
    public void syncUserNew(SyncOrgUserDTO dto) {

        // 有pushAppCode就是授权
        if (StringUtils.isNotEmpty(dto.getPushAppCode())) {
            // 存储最新的应用系统代码
            List<String> allAppCodes = Collections.synchronizedList(new ArrayList<>(Arrays.asList(dto.getPushAppCode().split(","))));

            // 记录新增移除失败的标记
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);
            // 新增移除是否含有量子
            AtomicBoolean hasLz = new AtomicBoolean(false);
            // 获取待同步的应用系统列表
            List<TSyncAppSystemManage> addAppList = getListByAppCode(dto.getAddAppCodes());
            List<TSyncAppSystemManage> updateAppList = getListByAppCode(dto.getUpdateAppCodes());
            List<TSyncAppSystemManage> deleteAppList = getListByAppCode(dto.getRemoveAppCodes());

            syncUserAuth(addAppList, allAppCodes, dto, atomicBoolean, "add");
            syncUserAuth(updateAppList, allAppCodes, dto, null, "update");
            syncUserAuth(deleteAppList, allAppCodes, dto, atomicBoolean, "delete");

            // 如果新增或移除存在失败的,则更新用户的应用系统代码列表
            if (atomicBoolean.get()) {
                String appCodeStr = StringUtils.join(allAppCodes, ",");
                // 更新用户信息中的应用系统代码列表
                cscpUserService.updateAppCodesById(dto.getUserId(), appCodeStr);
                //appAuthHistoryService.saveAppAuthByUser2(dto.getUserId(), dto.getCscpUserDetail(), allAppCodes);
            }

            // 量子的同步放最后，对于一个用户来说 量子的add和delete是互斥的
            syncUserLzAuth(addAppList, allAppCodes, dto, hasLz, "add");
            syncUserLzAuth(updateAppList, allAppCodes, dto, null, "update");
            if (!hasLz.get()) {
                syncUserLzAuth(deleteAppList, allAppCodes, dto, hasLz, "delete");
            }

        } else {
            // 单个用户单个应用同步
            syncUserNewDetail(dto);
        }
    }

    private void syncUserLzAuth(List<TSyncAppSystemManage> appList, List<String> allAppCodes, SyncOrgUserDTO dto, AtomicBoolean hasLz, String flag) {
        if (CollectionUtils.isEmpty(appList)) {
            return;
        }
        // flag
        dto.setFlag(flag);

        // 最后处理量子，注意避免app_push_code更新错误
        Optional<TSyncAppSystemManage> lzAppOpt = appList.stream()
                .filter(app -> HttpPushUserConsumer.LZ_ROUTING_KEY.equals(app.getAppCode()))
                .findFirst();

        if (lzAppOpt.isPresent()) {
            HttpPushUserDTO httpPushUserDTO = new HttpPushUserDTO();
            httpPushUserDTO.setAppSystemManage(lzAppOpt.get());

            String pushAppCode = StringUtils.join(allAppCodes, ",");
            httpPushUserDTO.setPushAppCode(pushAppCode);

            httpPushUserDTO.setUserId(dto.getUserId());
            httpPushUserDTO.setCscpUserDetail(dto.getCscpUserDetail());
            httpPushUserDTO.setFlag(dto.getFlag());
            httpPushUserDTO.setIsAutoPushFlag(dto.getIsAutoPushFlag());

            // 推送到量子特殊队列定时延迟消费
            httpPushUserProducer.httpPushUserToLzQueue(JSONObject.toJSONString(httpPushUserDTO));
            // 含有量子
            if (hasLz != null) {
                hasLz.set(true);
            }
        }
    }

    private void syncUserAuth(List<TSyncAppSystemManage> appList, List<String> allAppCodes, SyncOrgUserDTO dto, AtomicBoolean atomicBoolean, String flag) {
        if (CollectionUtils.isEmpty(appList)) {
            return;
        }
        // 设置flag
        dto.setFlag(flag);
        // 对非量子的每个应用系统创建并执行异步任务
        List<TSyncAppSystemManage> list = appList.stream().filter(tSyncAppSystemManage ->
                !HttpPushUserConsumer.LZ_ROUTING_KEY.equals(tSyncAppSystemManage.getAppCode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(list)) {
            // 存储所有异步操作的CompletableFuture对象
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            list.forEach(appSystemManage ->
                    futures.add(CompletableFuture.runAsync(() -> {
                        // 复制传入的用户信息到新的传输对象
                        SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                        BeanUtils.copyProperties(dto, syncOrgUserDTO);
                        // 关联当前应用系统信息
                        syncOrgUserDTO.setAppSystemManage(appSystemManage);
                        // 执行用户信息同步操作
                        if (!syncUserNewDetail(syncOrgUserDTO)) {
                            // 更新不影响授权字段
                            if (atomicBoolean != null) {
                                if ("add".equals(flag)) {
                                    // 如果新增失败 - 回退
                                    allAppCodes.remove(appSystemManage.getAppCode());
                                } else if ("delete".equals(flag)) {
                                    // 如果删除失败 - 回退
                                    allAppCodes.add(appSystemManage.getAppCode());
                                }
                                atomicBoolean.set(true);
                            }
                        }
                    })));
            // 等待所有异步任务完成
            futures.forEach(CompletableFuture::join);
        }
    }


    /**
     * 同步用户详情
     * 根据用户是否在系统内部分支处理同步逻辑
     *
     * @param dto 同步组织用户DTO，包含用户信息及应用系统管理信息
     * @return 同步结果，true表示同步成功，false表示同步失败
     */
    private boolean syncUserNewDetail(SyncOrgUserDTO dto) {
        boolean r = false;
        // 校验用户信息
        userInfoValid(dto);
        // 解析用户信息
        CscpUserDTO cscpUser = resolveCscpUser(dto);
        // 获取用户是否在系统内的标志
        Integer inSystemFlag = dto.getAppSystemManage().getInSystemFlag();
        // 根据用户是否在系统内，调用不同的同步方法
        if (OrgUserConstants.RequestMode.REQUEST_MODE_PULL_HTTP.equals(dto.getAppSystemManage().getRequestMode())) {
            PassiveSyncMessage message = new PassiveSyncMessage();
            message.setAppSystemId(dto.getAppId() != null ? dto.getAppId() : dto.getAppSystemManage().getId());
            message.setType(1);
            message.setUserId(cscpUser.getId());
            // 插入推送记录
            TSyncUserHistroyRecord record = this.insertUserSyncRecord(cscpUser, dto.getAppSystemManage(),  dto.getCscpUserDetail());
            message.setSyncHistoryId(record.getId());
            eventPublisher.publishEvent(message);
            return true;
        } else if (OrgUserConstants.RequestMode.REQUEST_MODE_MQ.equals(dto.getAppSystemManage().getRequestMode())) {
            this.mqSyncUser(dto);
            return true;
        }
        if (SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag) {
            r = syncOutUser(dto, cscpUser);
        } else if (SyncAppSystemEnum.NEI_BU.getCode() == inSystemFlag) {
            r = syncInUser(dto, cscpUser);
        }
        return r;
    }

    /**
     * 根据应用代码列表获取应用系统管理列表
     *
     * @param appCodes 应用代码列表
     * @return 应用系统管理列表，如果输入为空则返回空列表
     */
    public List<TSyncAppSystemManage> getListByAppCode(List<String> appCodes) {
        if (CollectionUtils.isEmpty(appCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TSyncAppSystemManage::getAppCode, appCodes);
        queryWrapper.eq(TSyncAppSystemManage::getStatus, 1);
        return tSyncAppSystemManageMapper.selectListNoAdd(queryWrapper);
    }

    /**
     * 自动同步用户到应用
     * 验证用户ID和应用代码，并处理应用代码变更
     *
     * @param cscpUserDTO 用户DTO，包含用户ID和应用代码
     * @param syncUpdate
     * @throws BusinessException 如果用户ID或应用代码为空，则抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void autoUserApp(CscpUserDTO cscpUserDTO, boolean syncUpdate) {
        if (cscpUserDTO.getId() == null || StringUtils.isEmpty(cscpUserDTO.getPushAppCode())) {
            throw new BusinessException("参数错误");
        }
        // 记录旧数据
        CscpUser oldUser = cscpUserService.getById(cscpUserDTO.getId());
        // 区分新增、更新、移除
        cscpUserDTO.buildAddAppCodes(oldUser.getPushAppCode());
        // 更新数据
        cscpUserService.updateAppCodesById(cscpUserDTO.getId(), cscpUserDTO.getPushAppCode());
        //appAuthHistoryService.saveAppAuthByUser3(cscpUserDTO.getId(), cscpUserDTO.getLoginName(), cscpUserDTO.getPushAppCode());

        autoUserAppDetail(cscpUserDTO, syncUpdate);
    }

    /**
     * 自动同步用户到应用的详细逻辑
     * 构建HTTP推送用户DTO并发送到队列
     *
     * @param cscpUserDTO 用户DTO，包含用户详情
     */
    @Override
    public void autoUserAppDetail(CscpUserDTO cscpUserDTO, boolean syncUpdate) {
        HttpPushUserDTO httpPushUserDTO = new HttpPushUserDTO();
        httpPushUserDTO.setAddAppCodes(StringUtils.join(cscpUserDTO.getAddAppCodes(), ","));
        if (syncUpdate) {
            httpPushUserDTO.setUpdateAppCodes(StringUtils.join(cscpUserDTO.getUpdateAppCodes(), ","));
        }
        httpPushUserDTO.setRemoveAppCodes(StringUtils.join(cscpUserDTO.getRemoveAppCodes(), ","));
        httpPushUserDTO.setPushAppCode(cscpUserDTO.getPushAppCode());
        httpPushUserDTO.setUserId(cscpUserDTO.getId());
        httpPushUserDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
        httpPushUserDTO.setIsAutoPushFlag(true);
        // 将用户信息发送到队列
        httpPushUserProducer.httpPushUserToQueue(JSONObject.toJSONString(httpPushUserDTO));
    }


    private void userInfoValid(SyncOrgUserDTO dto) {
        if (dto.getCscpUserDetail() == null) {
            throw new BusinessException("用户信息不能为空");
        }
        if (dto.getAppSystemManage() == null) {
            throw new BusinessException("推送应用信息不能为空");
        }
        if (dto.getUserId() == null) {
            throw new BusinessException("用户Id不能为空");
        }
    }

    private void retrySend(String syncUrl, Map<String, Object> bodyParams, BaseEntity baseEntity) {

        if (baseEntity instanceof TSyncUserHistroyRecord) {
            TSyncUserHistroyRecord insertRecord = new TSyncUserHistroyRecord();
            TSyncUserHistroyRecord insertRecord2 = (TSyncUserHistroyRecord) baseEntity;
            BeanUtils.copyProperties(insertRecord2, insertRecord);

            if ("false".equals(insertRecord.getSyncSuccess()) && "201".equals(insertRecord.getSyncStatus())
                    && ("add".equals(insertRecord.getStrOperaType()) || "update".equals(insertRecord.getStrOperaType()))) {
                SyncModel syncModel;
                try {
                    syncModel = (SyncModel) bodyParams.get("mode");
                } catch (Exception e) {
                    syncModel = JSONObject.parseObject(bodyParams.get("mode").toString(), SyncModel.class);
                }
                if (insertRecord.getSyncMessage() != null && insertRecord.getSyncMessage().contains("新增失败,用户已经存在")) {
                    syncModel.setStrOperaType("update");
                } else if (insertRecord.getSyncMessage() != null && insertRecord.getSyncMessage().contains("更新失败,用户不存在")) {
                    syncModel.setStrOperaType("add");
                } else {
                    return;
                }
                bodyParams.put("mode", syncModel);
                Pair<StatusLine, String> resp = null;
                try {
                    resp = WpsUtil.sendPostRequestConfigTimeout(syncUrl, null, bodyParams, 10000, 10000);
                    log.info("同步用户信息 响应状态码： {}", resp.getKey().getStatusCode());
                    JSONObject jsonObject = JSONObject.parseObject(resp.getValue());
                    SyncResponseModel responseModel = JSONObject.toJavaObject(jsonObject, SyncResponseModel.class);
                    //插入推送记录表
                    insertRecord.setId(null);
                    insertRecord.setStrOperaType(syncModel.getStrOperaType());
                    insertRecord.setOperateType(AUTO_PUSH);
                    if (resp.getKey().getStatusCode() == 200) {
                        insertRecord.setSyncSuccess("true");
                        insertRecord.setSyncMessage(responseModel.getMessage());
                        insertRecord.setSyncStatus(responseModel.getObj().getStatus());
                    } else {
                        insertRecord.setSyncSuccess("false");
                        insertRecord.setSyncMessage(resp.getValue());
                        insertRecord.setSyncStatus(resp.getKey().getStatusCode() + "");
                    }
                    // 手动设置create_time，确保分表路由正确
                    if (insertRecord.getCreateTime() == null) {
                        insertRecord.setCreateTime(LocalDateTime.now());
                    }
                    tSyncUserHistroyRecordMapper.insert(insertRecord);
                } catch (Exception e) {
                    log.error("重试同步用户信息请求接口失败：{}, {}", JSONObject.toJSONString(insertRecord), e.toString());
                }
            }
        } else if (baseEntity instanceof TSyncOrgHistroyRecord) {
            TSyncOrgHistroyRecord insertRecord = new TSyncOrgHistroyRecord();
            TSyncOrgHistroyRecord insertRecord2 = (TSyncOrgHistroyRecord) baseEntity;
            BeanUtils.copyProperties(insertRecord2, insertRecord);
            if ("false".equals(insertRecord.getSyncSuccess()) && "201".equals(insertRecord.getSyncStatus())
                    && ("add".equals(insertRecord.getStrOperaType()) || "update".equals(insertRecord.getStrOperaType()))) {
                SyncModel syncModel = null;
                try {
                    syncModel = (SyncModel) bodyParams.get("mode");
                } catch (Exception e) {
                    syncModel = JSONObject.parseObject(bodyParams.get("mode").toString(), SyncModel.class);
                }
                if (insertRecord.getSyncMessage() != null && insertRecord.getSyncMessage().contains("新增机构失败,该机构已经存在")) {
                    syncModel.setStrOperaType("update");
                } else if (insertRecord.getSyncMessage() != null && insertRecord.getSyncMessage().contains("更新失败")) {
                    syncModel.setStrOperaType("add");
                } else {
                    return;
                }
                bodyParams.put("mode", syncModel);
                Pair<StatusLine, String> resp = null;
                try {
                    resp = WpsUtil.sendPostRequestConfigTimeout(syncUrl, null, bodyParams, 10000, 10000);
                    log.info("同步机构信息 响应状态码： {}", resp.getKey().getStatusCode());
                    JSONObject jsonObject = JSONObject.parseObject(resp.getValue());
                    SyncResponseModel responseModel = JSONObject.toJavaObject(jsonObject, SyncResponseModel.class);
                    //插入推送记录表
                    insertRecord.setId(null);
                    insertRecord.setStrOperaType(syncModel.getStrOperaType());
                    if (resp.getKey().getStatusCode() == 200) {
                        insertRecord.setSyncSuccess("true");
                        insertRecord.setSyncMessage(responseModel.getMessage());
                        insertRecord.setSyncStatus(responseModel.getObj().getStatus());
                    } else {
                        insertRecord.setSyncSuccess("false");
                        insertRecord.setSyncMessage(resp.getValue());
                        insertRecord.setSyncStatus(resp.getKey().getStatusCode() + "");
                    }
                    tSyncOrgHistroyRecordMapper.insert(insertRecord);
                } catch (Exception e) {
                    log.error("重试同步机构信息请求接口失败：{}, {}", JSONObject.toJSONString(insertRecord), e.toString());
                }
            }
        }


    }

    /**
     * 检查应用绑定配置并处理用户权限
     *
     * @param loginUserName 当前用户名
     * @return 处理结果对象，包含是否直接返回、绑定应用名称等信息
     */
    private AppBindingCheckResult checkAppBindingConfig(String loginUserName) {
        AppBindingCheckResult result = new AppBindingCheckResult();

        List<Long> appIdList = itSyncAppModeratorManageService.getCurrentModeratorManageAppListByCompany(SecurityUtils.getCurrentUserId());

        if(CollUtil.isNotEmpty(appIdList)){
            // 当前用户是版主，则需要查询出自己管理的应用和非版主独立管理的应用
            result.setDirectReturn(true);
            List<TSyncAppSystemManage> appSystemManages = tSyncAppSystemManageMapper.selectListNoAdd(
                    Wrappers.lambdaQuery(TSyncAppSystemManage.class)
                            .and(wrapper -> wrapper
                                    .in(TSyncAppSystemManage::getAppId, appIdList)
                                    .or()
                                    .eq(TSyncAppSystemManage::getModeratorFlag, 0)
                            )
                            .eq(TSyncAppSystemManage::getStatus, 1)
                            .orderByDesc(TSyncAppSystemManage::getCreateTime));
            result.setAppData(appSystemManages);
        }else{
            // 当前用户不是版主，则查询非版主独立管理的应用
            List<Long> normalAppIdList = tSyncAppSystemManageMapper.selectListNoAdd(Wrappers.lambdaQuery(TSyncAppSystemManage.class)
                    .eq(TSyncAppSystemManage::getStatus, 1)
                    .eq(TSyncAppSystemManage::getModeratorFlag, 0)
                    .orderByDesc(TSyncAppSystemManage::getCreateTime))
                    .stream().map(TSyncAppSystemManage::getId).collect(Collectors.toList());
            result.setBindingAppIdList(normalAppIdList);
        }

        return result;
    }

    @Override
    public void autoOrgApp(CscpOrgDTO cscpOrgDTO, boolean syncUpdate) {
        if (cscpOrgDTO.getId() == null || StringUtils.isEmpty(cscpOrgDTO.getPushAppCode())) {
            throw new BusinessException("参数错误");
        }
        // 记录旧数据
        CscpOrg oldUser = cscpOrgService.getById(cscpOrgDTO.getId());
        // 区分新增、更新、移除
        cscpOrgDTO.buildAddAppCodes(oldUser.getPushAppCode());
        // 如果存在需要移除的appCode
        if (CollectionUtils.isNotEmpty(cscpOrgDTO.getRemoveAppCodes())) {
            // 校验一: 存在子级机构，则不允许移除授权
            LambdaQueryWrapper<CscpOrg> orgLqw = Wrappers.lambdaQuery();
            orgLqw.eq(CscpOrg::getParentId, cscpOrgDTO.getId());
            List<CscpOrg> orgList = cscpOrgService.list(orgLqw);
            if (CollectionUtils.isNotEmpty(orgList)) {
                for (CscpOrg cscpOrg : orgList) {
                    List<String> pushAppCode = Arrays.asList(cscpOrg.getPushAppCode().split(","));
                    List<String> containsList = pushAppCode.stream().filter(p -> cscpOrgDTO.getRemoveAppCodes().contains(p)).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(containsList)) {
                        throw new BusinessException("当前机构下,存在子级不能移除授权");
                    }
                }
            }

            // 校验二: 存在用户关联用户，则不允许移除授权
            LambdaQueryWrapper<CscpUserOrg> userOrgLqw = Wrappers.lambdaQuery();
            userOrgLqw.eq(CscpUserOrg::getId, cscpOrgDTO.getId());
            List<CscpUserOrg> userOrgList = cscpUserOrgService.selectListNoAdd(userOrgLqw);
            if (CollectionUtils.isNotEmpty(userOrgList)) {
                List<Long> userIds = userOrgList.stream().map(CscpUserOrg::getUserId).collect(Collectors.toList());
                LambdaQueryWrapper<CscpUser> userLqw = Wrappers.lambdaQuery();
                userLqw.in(CscpUser::getId, userIds);
                List<CscpUser> userList = cscpUserService.selectListNoAdd(userLqw);
                if (CollectionUtils.isNotEmpty(userList)) {
                    for (CscpUser cscpUser : userList) {
                        // 用户存在被移除的应用授权信息
                        List<String> pushAppCode = Arrays.asList(cscpUser.getPushAppCode().split(","));
                        List<String> containsList = pushAppCode.stream().filter(p -> cscpOrgDTO.getRemoveAppCodes().contains(p)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(containsList)) {
                            throw new BusinessException("取消机构授权失败, 因存在已授权用户数据");
                        }
                    }
                }
            }
        }
        // 更新数据
        cscpOrgService.updateAppCodesById(cscpOrgDTO.getId(), cscpOrgDTO.getPushAppCode());
        //appAuthHistoryService.saveAppAuthByOrg3(cscpOrgDTO.getId(), cscpOrgDTO.getOrgName(), cscpOrgDTO.getPushAppCode());

        autoOrgAppDetail(cscpOrgDTO, syncUpdate);
    }

    @Override
    public void syncOrgPush(SyncOrgUserDTO dto) {

        // 有pushAppCode就是授权
        if (StringUtils.isNotEmpty(dto.getPushAppCode())) {
            // 存储最新的应用系统代码
            List<String> allAppCodes = Collections.synchronizedList(new ArrayList<>(Arrays.asList(dto.getPushAppCode().split(","))));

            // 记录新增移除失败的标记
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);
            // 获取待同步的应用系统列表
            List<TSyncAppSystemManage> addAppList = getListByAppCode(dto.getAddAppCodes());
            List<TSyncAppSystemManage> updateAppList = getListByAppCode(dto.getUpdateAppCodes());
            List<TSyncAppSystemManage> deleteAppList = getListByAppCode(dto.getRemoveAppCodes());

            syncOrgAuth(addAppList, allAppCodes, dto, atomicBoolean, "add");
            syncOrgAuth(updateAppList, allAppCodes, dto, null, "update");
            syncOrgAuth(deleteAppList, allAppCodes, dto, atomicBoolean, "delete");

            // 如果新增或移除存在失败的,则更新机构的应用系统代码列表
            if (atomicBoolean.get()) {
                String appCodeStr = StringUtils.join(allAppCodes, ",");
                // 更新机构信息中的应用系统代码列表
                cscpOrgService.updateAppCodesById(dto.getOrgId(), appCodeStr);
                //appAuthHistoryService.saveAppAuthByOrg2(dto.getOrgId(), dto.getCscpUserDetail(), allAppCodes);
            }

        } else {
            // 单个机构单个应用同步
            syncOrgPushDetail(dto);
        }
    }

    private boolean syncOrgPushDetail(SyncOrgUserDTO dto) {
        boolean r = false;
        // 校验机构信息
        orgInfoValid(dto);
        // 解析机构信息
        CscpOrg cscpOrg = resolveCscpOrg(dto);
        cscpOrgService.handleOrgBelongCompanyInfo(cscpOrg);
        // 获取机构是否在系统内的标志
        Integer inSystemFlag = dto.getAppSystemManage().getInSystemFlag();
        // 被动推送模式-只有http 拉取
        if (OrgUserConstants.RequestMode.REQUEST_MODE_PULL_HTTP.equals(dto.getAppSystemManage().getRequestMode())) {
            itSyncAppSystemManagePullService.sendPullManages(dto.getAppSystemManage().getId(), OrgUserConstants.PushType.PUSH_UNIT, dto.getOrgId(), dto.getCscpUserDetail(), dto.getFlag());
            return true;
        } else if (SyncAppSystemEnum.WAI_BU.getCode() == inSystemFlag) {
        // 根据机构是否在系统内，调用不同的同步方法
            r = syncOutOrg(dto, cscpOrg);
        } else if (SyncAppSystemEnum.NEI_BU.getCode() == inSystemFlag) {
            // 内部推送-非被动模式 主动推送(MQ、HTTP)
            if (OrgUserConstants.RequestMode.REQUEST_MODE_MQ.equals(dto.getAppSystemManage().getRequestMode())) {
                r = syncInOrgMq(dto, cscpOrg);
            } else {
                r = syncInOrg(dto, cscpOrg);
            }
        }
        return r;
    }

    private void syncOrgAuth(List<TSyncAppSystemManage> appList, List<String> allAppCodes, SyncOrgUserDTO dto, AtomicBoolean atomicBoolean, String flag) {
        if (CollectionUtils.isEmpty(appList)) {
            return;
        }
        // 设置flag
        dto.setFlag(flag);
        if (CollectionUtils.isNotEmpty(appList)) {
            // 存储所有异步操作的CompletableFuture对象
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            appList.forEach(appSystemManage ->
                    futures.add(CompletableFuture.runAsync(() -> {
                        // 复制传入的用户信息到新的传输对象
                        SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                        BeanUtils.copyProperties(dto, syncOrgUserDTO);
                        // 关联当前应用系统信息
                        syncOrgUserDTO.setAppSystemManage(appSystemManage);
                        // 执行用户信息同步操作
                        if (!syncOrgPushDetail(syncOrgUserDTO)) {
                            // 更新不影响授权字段
                            if (atomicBoolean != null) {
                                if ("add".equals(flag)) {
                                    // 如果新增失败 - 回退
                                    allAppCodes.remove(appSystemManage.getAppCode());
                                } else if ("delete".equals(flag)) {
                                    // 如果删除失败 - 回退
                                    allAppCodes.add(appSystemManage.getAppCode());
                                }
                                atomicBoolean.set(true);
                            }
                        }
                    })));
            // 等待所有异步任务完成
            futures.forEach(CompletableFuture::join);
        }
    }

    private void orgInfoValid(SyncOrgUserDTO dto) {
        if (dto.getCscpUserDetail() == null) {
            throw new BusinessException("用户信息不能为空");
        }
        if (dto.getAppSystemManage() == null) {
            throw new BusinessException("推送应用信息不能为空");
        }
        if (dto.getOrgId() == null) {
            throw new BusinessException("机构Id不能为空");
        }
        if ("delete".equals(dto.getFlag())) {
            LambdaQueryWrapper<CscpUserOrg> userOrgLqw = Wrappers.lambdaQuery(CscpUserOrg.class);
            userOrgLqw.eq(CscpUserOrg::getOrgId, dto.getOrgId());
            Integer userCount = cscpUserOrgService.selectCountNoAdd(userOrgLqw);
            if (userCount > 0) {
                throw new BusinessException("存在用户信息,不能删除");
            }
            LambdaQueryWrapper<CscpOrg> orgLqw = Wrappers.lambdaQuery(CscpOrg.class);
            orgLqw.eq(CscpOrg::getParentId, dto.getOrgId());
            Integer orgCount = cscpOrgService.selectCountNoAdd(orgLqw);
            if (orgCount > 0) {
                throw new BusinessException("存在子级机构,不能删除");
            }
        }
    }

    private CscpOrg resolveCscpOrg(SyncOrgUserDTO dto) {
        // 若要加用户同步字段去xml中检查Base_Column_List
        return cscpOrgService.getById(dto.getOrgId());
    }

    private boolean syncOutOrg(SyncOrgUserDTO syncOrgUserDTO, CscpOrg cscpOrg) {
        TSyncAppSystemManage appSystemManage = syncOrgUserDTO.getAppSystemManage();

        if (WestoneUasManageService.WESTONE_UAS_SYSTEM.equals(appSystemManage.getAppCode())) {
            return true;
        }

        if (cscpOrg == null) {
            log.error("同步机构信息接口syncOutOrg，查不到机构信息：{}", syncOrgUserDTO.getUserId());
            return false;
        }

        // 3. 构建 SyncModel 和 SyncOrgUserModel
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_UNIT");

        // 设置操作类型
        syncModel.setStrOperaType(syncOrgUserDTO.getIsAutoPushFlag() ? syncOrgUserDTO.getFlag() : setOrgStrOperaType(appSystemManage, cscpOrg.getId()));

        // 构建机构模型数据
        SyncOrgUserModel syncOrgUserModel = buildSyncOrgUserModel(cscpOrg);

        // 构造请求参数 bodyParams
        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", syncOrgUserModel);

        log.info("同步机构信息至{}三方系统 请求报文： {}", appSystemManage.getAppName(), JSONObject.toJSONString(bodyParams));

        try {
            // 6. 发起 HTTP 请求
            SyncCommonHttpDTO syncCommonHttpDTO = new SyncCommonHttpDTO();
            syncCommonHttpDTO.setAppSystemManage(appSystemManage);
            syncCommonHttpDTO.setBodyParams(bodyParams);
            syncCommonHttpDTO.setSyncOrgUserDTO(syncOrgUserDTO);
            syncCommonHttpDTO.setCscpOrg(cscpOrg);
            // 设置用户信息方便后续使用
            if (syncOrgUserDTO.getCscpUserDetail() != null) {
                syncCommonHttpDTO.setCscpUserDetail(syncOrgUserDTO.getCscpUserDetail());
            } else {
                syncCommonHttpDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
            }

            // 外部的少
            return commonHttpRequestNew(syncCommonHttpDTO);
        } catch (Exception e) {
            log.error("同步用户信息请求接口失败：", e);
        }
        return false;
    }

    private boolean syncInOrg(SyncOrgUserDTO syncOrgUserDTO, CscpOrg cscpOrg) {
        // 准备机构同步数据
        SyncCommonHttpDTO syncCommonHttpDTO = prepareOrgSyncData(syncOrgUserDTO, cscpOrg);
        if (syncCommonHttpDTO == null) {
            return false;
        }

        try {
            // 执行HTTP同步请求
            return commonHttpRequestNew(syncCommonHttpDTO);
        } catch (Exception e) {
            log.error("syncInOrg error :{}", String.valueOf(e));
            return false;
        }
    }

    private boolean syncInOrgMq(SyncOrgUserDTO syncOrgUserDTO, CscpOrg cscpOrg) {
        // 准备机构同步数据
        SyncCommonHttpDTO syncCommonHttpDTO = prepareOrgSyncData(syncOrgUserDTO, cscpOrg);
        if (syncCommonHttpDTO == null) {
            return false;
        }

        // 新增推送记录
        TSyncOrgHistroyRecord insertRecord = this.setOrgHistory(
                syncCommonHttpDTO.getAppSystemManage(),
                cscpOrg,
                syncOrgUserDTO.getFlag(),
                syncCommonHttpDTO.getCscpUserDetail()
        );

        if (StrUtil.isNotBlank(syncOrgUserDTO.getOperateType())) {
            insertRecord.setOperateType(syncOrgUserDTO.getOperateType());
        } else {
            insertRecord.setOperateType(AUTO_PUSH);
        }

        insertRecord.setSourceId(syncOrgUserDTO.getSourceId());
        // 手动设置create_time，确保分表路由正确
        if (insertRecord.getCreateTime() == null) {
            insertRecord.setCreateTime(LocalDateTime.now());
        }
        tSyncOrgHistroyRecordMapper.insert(insertRecord);
        cscpOrg.setSyncHistoryId(insertRecord.getId());

        try {
            // 发送到MQ进行异步处理
            orgProducer.syncToProject(
                    syncCommonHttpDTO.getAppSystemManage().getAppRegionCode(cscpOrgRepository),
                    JSONObject.toJSONString(syncCommonHttpDTO.getBodyParams())
            );
            return true;
        } catch (Exception e) {
            log.error("syncInOrgMq error :{}", String.valueOf(e));
            return false;
        }
    }

    /**
     * 准备机构同步所需的通用数据
     * @param syncOrgUserDTO 同步机构用户DTO
     * @param cscpOrg 机构信息
     * @return 同步通用HTTP DTO
     */
    private SyncCommonHttpDTO prepareOrgSyncData(SyncOrgUserDTO syncOrgUserDTO, CscpOrg cscpOrg) {
        TSyncAppSystemManage appSystemManage = syncOrgUserDTO.getAppSystemManage();

        if (cscpOrg == null) {
            log.error("同步机构信息接口syncInOrg，查不到用户信息：{}", syncOrgUserDTO.getUserId());
            return null;
        }

        // 设置单位标识
        if (StrUtil.equals(OrgUserConstants.UnitType.SWBGTCODE, cscpOrg.getOrgCode()) ||
                (cscpOrg.getOrgCode() != null && cscpOrg.getOrgCode().contains(OrgUserConstants.UnitType.SWBGTCODE))) {
            cscpOrg.setUnitFlag("省委办公厅");
        } else {
            cscpOrg.setUnitFlag("");
        }

        // 设置机构缩写
        if (StrUtil.isBlank(cscpOrg.getOrgAbbreviation())) {
            cscpOrg.setOrgAbbreviation(cscpOrg.getOrgName());
        }

        // 构建 SyncModel
        SyncModel syncModel = new SyncModel();
        syncModel.setStrCodeType("PUSH_UNIT");
        // 设置操作类型
        syncModel.setStrOperaType(syncOrgUserDTO.getIsAutoPushFlag() ?
                syncOrgUserDTO.getFlag() :
                setOrgStrOperaType(appSystemManage, cscpOrg.getId())
        );

        // 构造请求参数 bodyParams
        Map<String, Object> bodyParams = new HashMap<>();
        bodyParams.put("mode", syncModel);
        bodyParams.put("dataInfo", cscpOrg);

        // 构建同步通用HTTP DTO
        SyncCommonHttpDTO syncCommonHttpDTO = new SyncCommonHttpDTO();
        syncCommonHttpDTO.setAppSystemManage(appSystemManage);
        syncCommonHttpDTO.setBodyParams(bodyParams);
        syncCommonHttpDTO.setSyncOrgUserDTO(syncOrgUserDTO);
        syncCommonHttpDTO.setCscpOrg(cscpOrg);

        // 设置用户详情
        if (syncOrgUserDTO.getCscpUserDetail() != null) {
            syncCommonHttpDTO.setCscpUserDetail(syncOrgUserDTO.getCscpUserDetail());
        } else {
            syncCommonHttpDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
        }

        return syncCommonHttpDTO;
    }



    private SyncOrgUserModel buildSyncOrgUserModel(CscpOrg cscpOrg) {
        SyncOrgUserModel syncOrgUserModel = new SyncOrgUserModel();
        if (StringUtils.isNotEmpty(cscpOrg.getStrId())) {
            syncOrgUserModel.setStrId(cscpOrg.getStrId());
            syncOrgUserModel.setStrTrustNo(cscpOrg.getStrTrustNo());
        } else {
            syncOrgUserModel.setStrId(cscpOrg.getId().toString());
            syncOrgUserModel.setStrTrustNo(cscpOrg.getId().toString());
        }
        if (cscpOrg.getParentId() == 0) {
            syncOrgUserModel.setStrParentId("-1");
            syncOrgUserModel.setStrParentTrustNo("-1");
        } else {
            CscpOrg parentOrg = cscpOrgService.selectOneNoAdd(new LambdaQueryWrapper<CscpOrg>().eq(CscpOrg::getId, cscpOrg.getParentId()));
            if (parentOrg != null && StringUtils.isNotEmpty(parentOrg.getStrId())) {
                syncOrgUserModel.setStrParentId(parentOrg.getStrId());
                syncOrgUserModel.setStrParentTrustNo(parentOrg.getStrTrustNo());
            } else {
                syncOrgUserModel.setStrParentId(cscpOrg.getParentId().toString());
                syncOrgUserModel.setStrParentTrustNo(cscpOrg.getParentId().toString());
            }
        }
        syncOrgUserModel.setStrUnitName(cscpOrg.getOrgName());
        syncOrgUserModel.setDtCreatDate(DateUtils.dateTimeToString(cscpOrg.getCreateTime()));
        syncOrgUserModel.setStrUnitCode(cscpOrg.getOrgCode());
        syncOrgUserModel.setStrDescription(cscpOrg.getDescription());
        syncOrgUserModel.setStrEasyName(cscpOrg.getOrgAbbreviation());
        syncOrgUserModel.setStrUnitAddress("");//单位地址
        syncOrgUserModel.setStrUnitNet("");//单位网址
        syncOrgUserModel.setStrUnitEmail("");//单位邮箱
        syncOrgUserModel.setStrPostalCode("");//邮政编码
        syncOrgUserModel.setStrAreaCode(cscpOrg.getRegionCode());
        syncOrgUserModel.setStrUnitPhone("");//单位电话
        syncOrgUserModel.setStrUnitFax("");//传真
        syncOrgUserModel.setIntSort(cscpOrg.getOrderBy());
        syncOrgUserModel.setStrRelationUser("");//单位联络人
        return syncOrgUserModel;
    }

    @Override
    public void autoOrgAppDetail(CscpOrgDTO cscpOrgDTO, boolean syncUpdate) {
        HttpPushOrgDTO httpPushOrgDTO = new HttpPushOrgDTO();
        httpPushOrgDTO.setAddAppCodes(StringUtils.join(cscpOrgDTO.getAddAppCodes(), ","));
        if (syncUpdate) {
            httpPushOrgDTO.setUpdateAppCodes(StringUtils.join(cscpOrgDTO.getUpdateAppCodes(), ","));
        }
        httpPushOrgDTO.setRemoveAppCodes(StringUtils.join(cscpOrgDTO.getRemoveAppCodes(), ","));
        httpPushOrgDTO.setPushAppCode(cscpOrgDTO.getPushAppCode());
        httpPushOrgDTO.setOrgId(cscpOrgDTO.getId());
        httpPushOrgDTO.setCscpUserDetail(SecurityUtils.getCurrentCscpUserDetail());
        httpPushOrgDTO.setIsAutoPushFlag(true);
        // 将机构信息发送到队列
        httpPushOrgProducer.httpPushOrgToQueue(JSONObject.toJSONString(httpPushOrgDTO));
    }

    @Override
    public void syncUserLz(SyncOrgUserDTO dto) {
        // 校验用户信息
        userInfoValid(dto);
        // 解析用户信息
        CscpUserDTO cscpUser = resolveCscpUser(dto);
        // 推送
        if (!syncInUser(dto, cscpUser)) {
            // 推送失败 且是授权过来的（授权过来的pushAppCode一定有值）
            if (StringUtils.isNotEmpty(dto.getPushAppCode())) {
                List<String> allAppCodes = Collections.synchronizedList(new ArrayList<>(Arrays.asList(dto.getPushAppCode().split(","))));
                if ("add".equals(dto.getFlag())) {
                    allAppCodes.remove(HttpPushUserConsumer.LZ_ROUTING_KEY);
                    String appCodeStr = StringUtils.join(allAppCodes, ",");
                    // 更新用户信息中的应用系统代码列表
                    cscpUserService.updateAppCodesById(dto.getUserId(), appCodeStr);
                    //appAuthHistoryService.saveAppAuthByUser2(dto.getUserId(), dto.getCscpUserDetail(), allAppCodes);
                } else if ("delete".equals(dto.getFlag())) {
                    allAppCodes.add(HttpPushUserConsumer.LZ_ROUTING_KEY);
                    String appCodeStr = StringUtils.join(allAppCodes, ",");
                    // 更新用户信息中的应用系统代码列表
                    cscpUserService.updateAppCodesById(dto.getUserId(), appCodeStr);
                    //appAuthHistoryService.saveAppAuthByUser2(dto.getUserId(), dto.getCscpUserDetail(), allAppCodes);
                }
            }

        }
    }

    @Override
    public PageResult<TSyncAppSystemManageDTO> unitAdminQueryAppPage(TSyncAppSystemManageDTO entityDTO, BasePageForm basePageForm) {

        if (SecurityUtils.isUnitAdmin()) {
            // 获取当前登录用户单位
            Long companyId = SecurityUtils.getCurrentCompanyId();
            entityDTO.setCompanyId(companyId);
            // 设置分页
            IPage<TSyncAppSystemManage> page = PageHelperUtil.getMPlusPageByBasePage(basePageForm);

            // 如果没配置系统动态参数，则按原有逻辑查询所有应用
            page = tSyncAppSystemManageMapper.unitAdminQueryAppPage(page, entityDTO);

            //返回
            IPage<TSyncAppSystemManageDTO> data = page.convert(entity -> BeanConvertUtils.copyProperties(entity, TSyncAppSystemManageDTO.class));
            data.getRecords().forEach(x -> {
                // 接口服务地址、appId和appKey脱敏展示
                if (StringUtils.isNotEmpty(x.getSyncUrl())) {
                    x.setSyncUrl(DesensitizeUtil.desensitizedUrl(x.getSyncUrl()));
                }
                if (StringUtils.isNotEmpty(x.getInSystemUrl())) {
                    x.setInSystemUrl(DesensitizeUtil.desensitizedUrl(x.getInSystemUrl()));
                }
                if (StringUtils.isNotEmpty(x.getAppId())) {
                    x.setAppId("******");
                }
                if (StringUtils.isNotEmpty(x.getAppKey())) {
                    x.setAppKey("******");
                }
            });
            return new PageResult<>(data.getRecords(),
                    data.getTotal(), data.getCurrent());
        }

        return queryListPage(entityDTO, basePageForm);
    }

    /**
     * 刪除把用戶的外网的appCod移除
     *
     * @param id
     */
    @Override
    public void updateUserserPushAppCode(Long id, List<TSyncAppSystemManage> appSystemManages) {
        List<TSyncAppSystemManage> list = appSystemManages.stream().filter(appSystemManage -> appSystemManage.getInSystemFlag() == 1).collect(Collectors.toList());
        String pc = "";
        if (CollectionUtils.isNotEmpty(list)) {
            pc = StringUtils.join(list.stream().map(TSyncAppSystemManage::getAppCode).collect(Collectors.toList()),  ",");
        }
        cscpUserService.updateAppCodesById(id, pc);
    }

    @Override
    public String selectEnableAppCodeStr() {
        LambdaQueryWrapper<TSyncAppSystemManage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TSyncAppSystemManage::getStatus, 1);
        queryWrapper.eq(TSyncAppSystemManage::getAutoPush, 1);
        queryWrapper.eq(TSyncAppSystemManage::getDeleted, 0);
        List<TSyncAppSystemManage> list = tSyncAppSystemManageMapper.selectListNoAdd(queryWrapper);
        if (CollectionUtils.isNotEmpty(list)) {
            return StringUtils.join(list.stream().map(TSyncAppSystemManage::getAppCode).collect(Collectors.toList()), ",");
        }
        return null;
    }

    @Override
    public List<TSyncAppSystemManage> getPassiveActiveApp() {

        LambdaQueryWrapper<TSyncAppSystemManage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(TSyncAppSystemManage::getRequestMode, 2);
        lambdaQueryWrapper.eq(TSyncAppSystemManage::getStatus, 1);
        return tSyncAppSystemManageMapper.selectListNoAdd(lambdaQueryWrapper);
    }

    @Override
    public TSyncUserHistroyRecord insertUserSyncRecord(CscpUserDTO cscpUserDTO, TSyncAppSystemManage app, CscpUserDetail cscpUserDetail) {
        if (cscpUserDetail == null) {
            cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
        }
        TSyncUserHistroyRecord userRecord = new TSyncUserHistroyRecord();
        userRecord.setAppId(app.getId());
        userRecord.setUserId(cscpUserDTO.getId());
        userRecord.setStrCname(cscpUserDTO.getRealName());
        userRecord.setStrUserId(cscpUserDTO.getLoginName());
        userRecord.setLoginName(cscpUserDTO.getLoginName());
        userRecord.setStrId(cscpUserDTO.getStrId());
        userRecord.setStrOperaType("add");
        userRecord.setRequestMode("2");
        userRecord.setCreateBy(cscpUserDetail.getId());
        userRecord.setCreateName(cscpUserDetail.getRealName());
        userRecord.setCreateTime(DateUtils.getLocalDateTime());
        userRecord.setDepartmentId(null != cscpUserDetail.getDepartmentId() ? cscpUserDetail.getDepartmentId() : null);
        userRecord.setCompanyId(null != cscpUserDetail.getCompanyId() ? cscpUserDetail.getCompanyId() : null);
        userRecord.setTenantId(null != cscpUserDetail.getTenantId() ? cscpUserDetail.getTenantId() : null);
        userRecord.setInSystemFlag(app.getInSystemFlag());
        userRecord.setStrMobile(cscpUserDTO.getMobile());
        // 同步中
        userRecord.setSyncSuccess("ing");
        userRecord.setSyncMessage("推送中");
        userRecord.setSyncStatus("205");
        tSyncUserHistroyRecordMapper.insert(userRecord);
        return userRecord;
    }

    /**
     * 批量设置应用的机构映射信息
     * @param records 应用记录列表
     */
    private void setOrgMapForRecords(List<TSyncAppSystemManageDTO> records) {
        if (records == null || records.isEmpty()) {
            return;
        }

        // 筛选出moderatorFlag为1的应用ID
        List<Long> appIds = records.stream()
                .filter(x -> ObjectUtil.isNotNull(x.getRoleId()))
                .map(TSyncAppSystemManageDTO::getId)
                .collect(Collectors.toList());

        if (appIds.isEmpty()) {
            return;
        }

        // 批量查询关联的机构信息
        List<TSyncAppSystemManageCompany> companyList = tSyncAppSystemManageMapper.selectByManageCompanyAppIds(appIds);

        // 按appId分组
        Map<Long, List<TSyncAppSystemManageCompany>> companyMap = companyList.stream()
                .collect(Collectors.groupingBy(TSyncAppSystemManageCompany::getAppId));

        // 为每个应用设置机构映射
        records.forEach(record -> {
            if (ObjectUtil.isNotNull(record.getRoleId())) {
                List<TSyncAppSystemManageCompany> companies = companyMap.get(record.getId());
                if (companies != null && !companies.isEmpty()) {
                    // 将机构信息转换为Map<orgId, orgName>的形式
                    Map<Long, String> orgMap = companies.stream()
                            .filter(company -> ObjectUtil.isNotNull(company.getOrgId()) && StringUtils.isNotEmpty(company.getOrgName()))
                            .collect(Collectors.toMap(
                                    TSyncAppSystemManageCompany::getOrgId,
                                    TSyncAppSystemManageCompany::getOrgName,
                                    (existing, replacement) -> existing // 如果有重复的orgId，保留第一个
                            ));
                    record.setOrgMap(orgMap);
                }
            }
        });
    }

    /**
     * [辅助方法] 根据应用的新角色，同步版主信息 (先全量逻辑删除，再全量插入)
     *
     * @param appId     应用ID
     * @param newRoleId 新的角色ID, 可能为null
     */
    private void syncModeratorsForApp(Long appId, Long newRoleId) {
        // 逻辑删除该应用下所有现存的版主
        log.info("正在为应用(appId:{})进行版主同步，首先逻辑删除所有旧版主...", appId);
        LambdaUpdateWrapper<TSyncAppModeratorManage> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(TSyncAppModeratorManage::getAppId, appId)
                .set(TSyncAppModeratorManage::getDeleted, 1); // 设置 deleted = 1
        itSyncAppModeratorManageService.update(null, updateWrapper);

        // 如果新的角色ID不为空，则根据新角色下的用户添加新的版主
        if (newRoleId != null) {
            log.info("为应用(appId:{})绑定新角色(roleId:{})下的用户作为版主", appId, newRoleId);

            // 根据新角色ID查询所有关联的用户ID
            List<Long> userIds = tSyncAppSystemManageMapper.selectUserIdsByRoleId(newRoleId);

            if (CollectionUtils.isNotEmpty(userIds)) {
                // 构建新的版主列表
                List<TSyncAppModeratorManage> newModerators = userIds.stream().map(userId -> {
                    TSyncAppModeratorManage newModerator = new TSyncAppModeratorManage();
                    newModerator.setAppId(appId);
                    newModerator.setModeratorId(userId);
                    return newModerator;
                }).collect(Collectors.toList());

                // 批量插入新的版主
                itSyncAppModeratorManageService.saveBatch(newModerators);
                log.info("成功为应用(appId:{})添加了{}名新版主", appId, newModerators.size());
            } else {
                log.info("新角色(roleId:{})下没有关联的用户, 无需添加新版主", newRoleId);
            }
        }
    }

    /**
     *  设置用户所属机构、机构类型，所在单位统一社会信用代码
     * */
    private void handleFillOrgInfo(List<CscpUserOrg> cscpUserOrgs, CscpUserDTO cscpUserDTO) {
        if (CollUtil.isNotEmpty(cscpUserOrgs) && cscpUserDTO != null) {
            // 合并需要查的ID
            Set<Long> ids = new HashSet<>();
            cscpUserOrgs.forEach(e -> {
                if (e.getCompanyId() != null) ids.add(e.getCompanyId());
                if (e.getOrgId() != null) ids.add(e.getOrgId());
            });

            // id->CscpOrg
            Map<Long, CscpOrg> orgMap = cscpOrgRepository.selectListNoAdd(
                    new LambdaQueryWrapper<CscpOrg>()
                            .select(CscpOrg::getId, CscpOrg::getCreditCode, CscpOrg::getType)
                            .in(CscpOrg::getId, ids)
            ).stream().collect(Collectors.toMap(CscpOrg::getId, o -> o, (v1, v2) -> v1));

            // 1. 信用代码，只收companyId（type=2）
            Map<Long, String> creditCodeMap = cscpUserOrgs.stream()
                    .map(CscpUserOrg::getCompanyId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .filter(id -> {
                        CscpOrg org = orgMap.get(id);
                        return org != null && ObjectUtil.equal(org.getType(), 2);
                    })
                    .collect(Collectors.toMap(id -> id, id -> StringUtils.defaultString(orgMap.get(id).getCreditCode(), "")));

            cscpUserDTO.setCreditCodeMap(creditCodeMap);

            // 2. 只设置orgType给cscpUserOrgs中每个orgId
            for (CscpUserOrg userOrg : cscpUserOrgs) {
                CscpOrg org = orgMap.get(userOrg.getOrgId());
                userOrg.setOrgType(org == null ? null : org.getType());
            }

            cscpUserDTO.setOrgList(cscpUserOrgs);
        } else {
            cscpUserDTO.setCreditCodeMap(new HashMap<>());
        }

        if (cscpUserDTO != null && StringUtils.isEmpty(cscpUserDTO.getStrId())) {
            cscpUserDTO.setStrId(cscpUserDTO.getId().toString());
        }
    }

}

package com.ctsi.hndx.meeting.entity.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.ctsi.hndx.common.BaseDtoEntity;

import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-22
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TMeetingDTO对象", description = "")
public class TMeetingDTO extends BaseDtoEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @ApiModelProperty(value = "会议标题", required = true)
    @NotNull(message = "会议标题不能为空")
    private String conferenceTitle;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间", required = true)
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间", required = true)
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime endTime;

    /**
     * 会议室id
     */
    @ApiModelProperty(value = "会议室id", required = true)
    @NotNull(message = "会议室id不能为空")
    private Long conferenceRoomId;

    /**
     * 会议室名称
     */
    @ApiModelProperty(value = "会议室名称", required = true)
    @NotNull(message = "会议室名称不能为空")
    private String conferenceRoomName;

    /**
     * 联系人id
     */
    @ApiModelProperty(value = "联系人id")
    private Long contactsId;

    /**
     * 联系人名称
     */
    @ApiModelProperty(value = "联系人名称")
    private String contactsName;

    /**
     * 联系号码
     */
    @ApiModelProperty(value = "联系号码")
    private String contactNumber;

    /**
     * 出席人员
     */
    @ApiModelProperty(value = "出席人员")
    private String attendants;

    /**
     * 会议内容
     */
    @ApiModelProperty(value = "会议内容")
    private String meetingContent;

    /**
     * 会议室状态 （0：待审批 1：已通过 2：已驳回 3：占用）
     */
    @ApiModelProperty(value = "会议室状态 （0：待审批 1：已通过 2：已驳回 3：占用 4.取消）")
    private Integer conferenceState;

    /**
     * 驳回原因
     */
    @ApiModelProperty(value = "驳回原因")
    private String rejectReason;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    private Integer sort;

    /**
     * 会议室负责人id
     */
    @ApiModelProperty(value = "会议室负责人id")
    private Long administratorsId;

    /**
     * 会议室负责人
     */
    @ApiModelProperty(value = "会议室负责人")
    private String administratorsName;


    /**
     * 会议室申请人
     */
    @ApiModelProperty(value = "会议室申请人")
    private String createName;

    /**
     * 会议室创建时间
     */
    @ApiModelProperty(value = "会议室创建时间")
    private LocalDateTime createTime;


    @ApiModelProperty(value = "单位id")
    private Long companyId;
}
